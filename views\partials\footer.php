</div>
    </main>

    <!-- Footer -->
    <?php if (in_array($page, ['landing', 'about', 'contact', 'privacy', 'terms'])): ?>
    <footer class="bg-dark text-white py-5 mt-auto">
        <div class="container">
            <div class="row g-4">
                <div class="col-md-4">
                    <h4 class="fw-bold mb-4"><?php echo getSetting('app_name', 'TimeTracker Pro'); ?></h4>
                    <p class="mb-0">The next-generation time tracking solution for teams and businesses of all sizes.</p>
                </div>
                <div class="col-md-2">
                    <h5 class="fw-bold mb-3">Company</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="<?php echo BASE_URL; ?>?page=about" class="text-white text-decoration-none">About Us</a></li>
                        <li class="mb-2"><a href="<?php echo BASE_URL; ?>?page=careers" class="text-white text-decoration-none">Careers</a></li>
                        <li class="mb-2"><a href="<?php echo BASE_URL; ?>?page=blog" class="text-white text-decoration-none">Blog</a></li>
                        <li class="mb-2"><a href="<?php echo BASE_URL; ?>?page=contact" class="text-white text-decoration-none">Contact</a></li>
                        <li class="mb-2"><a href="<?php echo BASE_URL; ?>?page=privacy" class="text-white text-decoration-none">Privacy Policy</a></li>
                        <li class="mb-2"><a href="<?php echo BASE_URL; ?>?page=terms" class="text-white text-decoration-none">Terms of Service</a></li>
                    </ul>
                </div>
                <div class="col-md-2">
                    <h5 class="fw-bold mb-3">Product</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#features" class="text-white text-decoration-none">Features</a></li>
                        <li class="mb-2"><a href="#pricing" class="text-white text-decoration-none">Pricing</a></li>
                        <li class="mb-2"><a href="<?php echo BASE_URL; ?>?page=integrations" class="text-white text-decoration-none">Integrations</a></li>
                        <li class="mb-2"><a href="<?php echo BASE_URL; ?>?page=api-docs" class="text-white text-decoration-none">API</a></li>
                        <li class="mb-2"><a href="<?php echo BASE_URL; ?>?page=security" class="text-white text-decoration-none">Security</a></li>
                        <li class="mb-2"><a href="<?php echo BASE_URL; ?>?page=help" class="text-white text-decoration-none">Help Center</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5 class="fw-bold mb-3">Stay Connected</h5>
                    <p class="mb-3">Subscribe to our newsletter for updates and tips.</p>
                    
                    <!-- Newsletter Subscription Form with AJAX handling -->
                    <form id="newsletter-form" method="post" action="<?php echo BASE_URL; ?>">
                        <div class="d-flex">
                            <input type="email" name="email" class="form-control me-2" placeholder="Your email" required>
                            <input type="hidden" name="action" value="subscribe_newsletter">
                            <button type="submit" class="btn btn-primary">Subscribe</button>
                        </div>
                        <div id="newsletter-message" class="mt-2 small"></div>
                    </form>
                    
                    <div class="mt-4">
                        <a href="<?php echo getSetting('facebook_url', '#'); ?>" target="_blank" class="text-white me-3" aria-label="Follow us on Facebook"><i class="fab fa-facebook-f fa-lg"></i></a>
                        <a href="<?php echo getSetting('twitter_url', '#'); ?>" target="_blank" class="text-white me-3" aria-label="Follow us on Twitter"><i class="fab fa-twitter fa-lg"></i></a>
                        <a href="<?php echo getSetting('linkedin_url', '#'); ?>" target="_blank" class="text-white me-3" aria-label="Follow us on LinkedIn"><i class="fab fa-linkedin-in fa-lg"></i></a>
                        <a href="<?php echo getSetting('instagram_url', '#'); ?>" target="_blank" class="text-white" aria-label="Follow us on Instagram"><i class="fab fa-instagram fa-lg"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4 bg-light">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo getSetting('company_name', getSetting('app_name', 'TimeTracker Pro')); ?>. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Version <?php echo defined('APP_VERSION') ? APP_VERSION : '1.0'; ?></p>
                </div>
            </div>
        </div>
    </footer>
    <?php else: ?>
    <footer class="bg-light py-3 mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?php echo date('Y'); ?> <?php echo defined('APP_NAME') ? APP_NAME : getSetting('app_name', 'TimeTracker Pro'); ?>. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Version <?php echo defined('APP_VERSION') ? APP_VERSION : '1.0'; ?></p>
                </div>
            </div>
        </div>
    </footer>
    <?php endif; ?>

    <!-- Optimized Script Loading -->
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js" defer></script>

    <!-- jQuery (load async if not needed immediately) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js" async></script>

    <!-- Custom JS -->
    <script src="<?php echo BASE_URL; ?>assets/js/script.js" defer></script>

    <?php if ($page === 'landing'): ?>
    <!-- Landing Page JS -->
    <script src="<?php echo BASE_URL; ?>assets/js/landing.js" defer></script>
    
    <!-- A/B Testing Script -->
    <?php if (getSetting('enable_ab_testing', 'false') === 'true'): ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Simple A/B testing for CTA buttons
        if (Math.random() > 0.5) {
            // Variant B: Change CTA text and color
            var primaryCtaButtons = document.querySelectorAll('.pulse-btn');
            primaryCtaButtons.forEach(function(btn) {
                if (btn.textContent.includes('Get Started')) {
                    btn.innerHTML = btn.innerHTML.replace('Get Started', 'Start Free Trial');
                    btn.classList.add('btn-success');
                    btn.classList.remove('btn-primary');
                    
                    // Log the variant for analytics
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'view_variant', {
                            'event_category': 'A/B Test',
                            'event_label': 'CTA Variant B'
                        });
                    }
                }
            });
        } else {
            // Log the control for analytics
            if (typeof gtag !== 'undefined') {
                gtag('event', 'view_variant', {
                    'event_category': 'A/B Test',
                    'event_label': 'CTA Variant A'
                });
            }
        }
        
        // Track CTA clicks
        document.querySelectorAll('.hero-buttons a, .pricing-footer a, .cta-section a').forEach(function(btn) {
            btn.addEventListener('click', function() {
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'click', {
                        'event_category': 'CTA',
                        'event_label': this.textContent.trim()
                    });
                }
            });
        });
    });
    </script>
    <?php endif; ?>
    <?php endif; ?>
</body>
</html>
