<?php
/**
 * Alternative Routes Configuration
 * This file adds the alternative page routes to the application
 */

// Add page routes to the route configuration
function registerAlternativeRoutes(&$routes) {
    // Main auth pages (alternative design is now default)
    $routes['login'] = [
        'view' => 'auth/login-alternative.php',
        'auth' => false,
        'title' => 'Login - TimeTracker'
    ];
    
    $routes['register'] = [
        'view' => 'auth/register-alternative.php',
        'auth' => false,
        'title' => 'Register - TimeTracker'
    ];
    
    $routes['forgot-password'] = [
        'view' => 'auth/forgot-password-alternative.php',
        'auth' => false,
        'title' => 'Forgot Password - TimeTracker'
    ];
    
    $routes['reset-password'] = [
        'view' => 'auth/reset-password-alternative.php',
        'auth' => false,
        'title' => 'Reset Password - TimeTracker'
    ];
    
    // Original versions as "-original" suffix
    $routes['login-original'] = [
        'view' => 'auth/login.php',
        'auth' => false,
        'title' => 'Login (Original) - TimeTracker'
    ];
    
    $routes['register-original'] = [
        'view' => 'auth/register.php',
        'auth' => false,
        'title' => 'Register (Original) - TimeTracker'
    ];
    
    $routes['forgot-password-original'] = [
        'view' => 'auth/forgot-password.php',
        'auth' => false,
        'title' => 'Forgot Password (Original) - TimeTracker'
    ];
    
    // Other pages
    $routes['landing'] = [
        'view' => 'landing-alternative.php',
        'auth' => false,
        'title' => 'TimeTracker - Next Generation Time Tracking'
    ];
    
    $routes['landing-original'] = [
        'view' => 'landing.php',
        'auth' => false,
        'title' => 'TimeTracker (Original) - Next Generation Time Tracking'
    ];
    
    $routes['design-comparison'] = [
        'view' => 'design-comparison.php',
        'auth' => false,
        'title' => 'Design Comparison - TimeTracker'
    ];
    
    return $routes;
}

// Include theme switcher script in the head section
function includeThemeSwitcher() {
    echo '<script src="' . BASE_URL . 'assets/js/theme-switcher.js"></script>';
}

// Determine if the current page is using the original design
function isAlternativeDesign() {
    $page = isset($_GET['page']) ? $_GET['page'] : '';
    // Alternative design is now default, so only return false for -original pages
    return !(strpos($page, '-original') !== false);
}

// Helper function to add links to alternative styles in page header
function addAlternativeStylesheets() {
    if (isAlternativeDesign()) {
        echo '<link rel="stylesheet" href="' . BASE_URL . 'assets/css/landing-alternative.css">';
        
        // Check if we're on an auth page
        $page = isset($_GET['page']) ? $_GET['page'] : '';
        if (strpos($page, 'login') !== false || strpos($page, 'register') !== false || 
            strpos($page, 'forgot-password') !== false || strpos($page, 'reset-password') !== false) {
            echo '<link rel="stylesheet" href="' . BASE_URL . 'assets/css/auth-alternative.css">';
        }
    }
}

// Get the correct page version based on the user's theme preference
function getThemeBasedPage($originalPage, $alternativePage) {
    $theme = isset($_COOKIE['timeTracker_theme']) ? $_COOKIE['timeTracker_theme'] : 'alternative';
    return ($theme === 'alternative') ? $alternativePage : $originalPage;
}

// Get the equivalent alternative or original version of the current page
function getAlternativeVersionUrl() {
    $currentUrl = $_SERVER['REQUEST_URI'];
    $page = $_GET['page'] ?? 'landing';
    
    if (isAlternativeDesign()) {
        // Convert from alternative to original
        if ($page === 'landing-alternative' || strpos($_SERVER['SCRIPT_NAME'], 'landing-alternative.php') !== false) {
            return BASE_URL;
        } else {
            $originalPage = str_replace('-alternative', '', $page);
            return BASE_URL . '?page=' . $originalPage;
        }
    } else {
        // Convert from original to alternative
        if ($page === 'landing' || $page === '') {
            return BASE_URL . 'landing-alternative.php';
        } else {
            return BASE_URL . '?page=' . $page . '-alternative';
        }
    }
}
?>
