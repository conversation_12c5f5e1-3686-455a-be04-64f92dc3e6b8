/**
 * Navigation CSS for TimeTracker alternative design
 * This file contains all navbar-specific styles for the alternative design
 */

.navbar-alt {
    transition: var(--transition-alt);
    padding: 1rem 2rem;
}

.navbar-transparent-alt {
    background-color: transparent !important;
    box-shadow: none;
}

.navbar-scrolled-alt {
    background-color: var(--primary-alt) !important;
    box-shadow: var(--shadow-md-alt);
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}

.navbar-brand-alt {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--text-light-alt) !important;
    letter-spacing: -0.05em;
}

.navbar-nav-alt .nav-link {
    color: var(--text-light-alt);
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: var(--transition-alt);
    border-radius: var(--border-radius-alt);
    margin: 0 0.25rem;
}

.navbar-nav-alt .nav-link:hover,
.navbar-nav-alt .nav-link:focus,
.navbar-nav-alt .nav-link.active {
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-1px);
}

.navbar-toggler-alt {
    color: var(--text-light-alt);
    border-color: rgba(255, 255, 255, 0.2);
    padding: 0.5rem;
}

.navbar-toggler-alt:focus {
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.2);
}

/* Mobile menu styles */
@media (max-width: 991.98px) {
    .navbar-collapse-alt {
        background-color: var(--primary-alt);
        border-radius: var(--border-radius-alt);
        padding: 1rem;
        margin-top: 0.5rem;
        box-shadow: var(--shadow-md-alt);
        max-height: 85vh;
        overflow-y: auto;
    }
    
    .navbar-nav-alt .nav-link {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .navbar-nav-alt .nav-item:last-child .nav-link {
        border-bottom: none;
    }
    
    .navbar-transparent-alt {
        background-color: var(--primary-alt) !important;
    }
}
