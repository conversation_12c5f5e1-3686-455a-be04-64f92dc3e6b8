<?php
// Process reset password form
$error = '';
$success = '';

// Check if token is provided
$token = $_GET['token'] ?? '';

if (empty($token)) {
    $error = "Invalid or missing reset token";
} else {
    // In a real application, you would validate the token against the database
    // For this example, we'll just check if it's not empty
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $password = $_POST['password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        if (empty($password) || empty($confirmPassword)) {
            $error = "Both password fields are required";
        } elseif ($password !== $confirmPassword) {
            $error = "Passwords do not match";
        } elseif (strlen($password) < 6) {
            $error = "Password must be at least 6 characters long";
        } else {
            // In a real application, you would:
            // 1. Validate the token
            // 2. Update the user's password in the database
            // 3. Invalidate the reset token
            // For this example, we'll just show a success message
            $success = "Your password has been successfully reset. You can now <a href='" . BASE_URL . "?page=login'>login</a> with your new password.";
        }
    }
}
?>

<div class="back-to-home">
    <a href="<?php echo BASE_URL; ?>"><i class="fas fa-arrow-left"></i> Back to Home</a>
</div>

<div class="auth-card">
    <div class="card-header">
        <h4 class="mb-0">Reset Your Password</h4>
    </div>
    <div class="card-body">
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
            <div class="text-center mt-4">
                <a href="<?php echo BASE_URL; ?>?page=login" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i> Go to Login
                </a>
            </div>
        <?php elseif (!empty($token)): ?>
            <p class="mb-4">Enter your new password below.</p>
            
            <form method="post">
                <div class="mb-4">
                    <label for="password" class="form-label">New Password</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        <input type="password" class="form-control" id="password" name="password" placeholder="Enter new password" required>
                    </div>
                    <div class="form-text">Password must be at least 6 characters long.</div>
                </div>
                
                <div class="mb-4">
                    <label for="confirm_password" class="form-label">Confirm New Password</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Confirm new password" required>
                    </div>
                </div>
                
                <div class="d-grid gap-2 mb-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-key me-2"></i> Reset Password
                    </button>
                </div>
            </form>
        <?php endif; ?>
        
        <div class="text-center">
            <p class="mb-0">
                Remember your password? <a href="<?php echo BASE_URL; ?>?page=login" class="fw-bold"><i class="fas fa-sign-in-alt me-1"></i> Back to Login</a>
            </p>
        </div>
    </div>
</div>
