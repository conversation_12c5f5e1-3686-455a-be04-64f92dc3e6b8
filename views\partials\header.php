<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getSetting('app_name', 'TimeTracker Pro'); ?><?php echo isset($_SESSION['company_name']) ? ' - ' . $_SESSION['company_name'] : ''; ?></title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="Track time, boost productivity, and simplify billing with <?php echo getSetting('app_name', 'TimeTracker Pro'); ?>. The next-generation time tracking solution for teams and businesses of all sizes.">
    <meta name="keywords" content="time tracking, project management, productivity, billing, timesheet, team management">
    <meta name="author" content="<?php echo getSetting('company_name', 'TimeTracker Inc.'); ?>">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo BASE_URL; ?>">
    <meta property="og:title" content="<?php echo getSetting('app_name', 'TimeTracker Pro'); ?> - Track Time. Boost Productivity. Simplify Billing.">
    <meta property="og:description" content="The next-generation time tracking solution for teams and businesses of all sizes. Streamline your workflow, increase accountability, and make data-driven decisions.">
    <meta property="og:image" content="<?php echo BASE_URL; ?>assets/img/og-image.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo BASE_URL; ?>">
    <meta property="twitter:title" content="<?php echo getSetting('app_name', 'TimeTracker Pro'); ?> - Track Time. Boost Productivity. Simplify Billing.">
    <meta property="twitter:description" content="The next-generation time tracking solution for teams and businesses of all sizes. Streamline your workflow, increase accountability, and make data-driven decisions.">
    <meta property="twitter:image" content="<?php echo BASE_URL; ?>assets/img/twitter-image.jpg">

    <!-- Critical CSS for faster initial render -->
    <?php if ($page === 'landing'): ?>
    <style>
        <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/' . BASE_URL . 'assets/css/critical/landing-critical.css'); ?>
    </style>
    <?php endif; ?>

    <!-- Preload Fonts with font-display strategy -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@500;600;700;800&display=swap&display=swap&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@500;600;700;800&display=swap&display=swap&display=swap" rel="stylesheet"></noscript>

    <!-- Font-Display Fallback -->
    <style>
        @font-face {
            font-family: 'Poppins Fallback';
            font-style: normal;
            font-weight: 400;
            src: local('Arial');
            font-display: swap;
        }
        @font-face {
            font-family: 'Montserrat Fallback';
            font-style: normal;
            font-weight: 700;
            src: local('Arial Bold');
            font-display: swap;
        }
        html { font-family: 'Poppins Fallback', sans-serif; }
        h1, h2, h3, h4, h5, h6 { font-family: 'Montserrat Fallback', sans-serif; }
    </style>

    <!-- Resource Hints for Third-Party Domains -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>

    <!-- Bootstrap CSS -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"></noscript>

    <!-- Font Awesome -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"></noscript>

    <!-- Custom CSS (loaded asynchronously after critical CSS) -->
    <link rel="preload" href="<?php echo BASE_URL; ?>assets/css/style.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/style.css"></noscript>

    <?php if ($page === 'landing'): ?>
    <link rel="preload" href="<?php echo BASE_URL; ?>assets/css/landing.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/landing.css"></noscript>
    <?php endif; ?>

    <!-- Preload critical JS -->
    <link rel="preload" href="<?php echo BASE_URL; ?>assets/js/landing.js" as="script">

    <!-- Favicon -->
    <link rel="icon" href="<?php echo BASE_URL; ?>assets/img/favicon.ico">

    <!-- Fast CSS Loading Helper -->
    <script>
        // Tiny script to handle CSS onload in older browsers
        var loadCSS = function(href) {
            var l = document.createElement('link');
            l.rel = 'stylesheet';
            l.href = href;
            document.head.appendChild(l);
        };
        var raf = window.requestAnimationFrame || function(cb) { return setTimeout(cb, 0); };
        // Fallback for non-JS browsers
        if (document.querySelector('noscript') && 'querySelector' in document) {
            raf(function() {
                var elements = document.querySelectorAll('link[rel="preload"][as="style"]');
                for (var i = 0; i < elements.length; i++) {
                    loadCSS(elements[i].href);
                }
            });
        }
    </script>

    <?php if ($page === 'landing'): ?>
    <!-- Structured Data for Search Engines -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "<?php echo getSetting('app_name', 'TimeTracker Pro'); ?>",
      "applicationCategory": "BusinessApplication",
      "operatingSystem": "Web",
      "offers": {
        "@type": "Offer",
        "price": "9.00",
        "priceCurrency": "USD"
      },
      "description": "The next-generation time tracking solution for teams and businesses of all sizes. Streamline your workflow, increase accountability, and make data-driven decisions.",
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "ratingCount": "127"
      }
    }
    </script>

    <!-- Analytics Tracking Code -->
    <?php if (getSetting('enable_analytics', 'true') === 'true'): ?>
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo getSetting('google_analytics_id', 'UA-XXXXXXXX-X'); ?>"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '<?php echo getSetting('google_analytics_id', 'UA-XXXXXXXX-X'); ?>');

      <?php if ($page === 'landing'): ?>
      // Track landing page sections viewed
      document.addEventListener('DOMContentLoaded', function() {
          const sections = ['home', 'features', 'how-it-works', 'testimonials', 'pricing'];
          const observerOptions = {
              root: null,
              rootMargin: '0px',
              threshold: 0.5
          };

          const sectionObserver = new IntersectionObserver(function(entries) {
              entries.forEach(entry => {
                  if (entry.isIntersecting) {
                      const sectionId = entry.target.id;
                      gtag('event', 'view_section', {
                          'event_category': 'Landing Page',
                          'event_label': sectionId
                      });
                  }
              });
          }, observerOptions);

          sections.forEach(sectionId => {
              const section = document.getElementById(sectionId);
              if (section) {
                  sectionObserver.observe(section);
              }
          });
      });
      <?php endif; ?>
    </script>
    <?php endif; ?>
    <?php endif; ?>
</head>
<body class="<?php echo $page === 'landing' ? 'landing-page' : ''; ?>">
    <?php if ($page === 'landing'): ?>
    <!-- Landing Page Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark <?php echo isLoggedIn() ? 'bg-primary' : 'bg-transparent position-absolute w-100 top-0 start-0 z-3'; ?>">
        <div class="container">
            <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                <?php echo getSetting('app_name', 'TimeTracker Pro'); ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#features">Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#how-it-works">How It Works</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#testimonials">Testimonials</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#pricing">Pricing</a>
                    </li>
                </ul>

                <div class="d-flex">
                    <a href="<?php echo BASE_URL; ?>?page=login" class="btn btn-outline-light me-2">
                        <i class="fas fa-sign-in-alt me-1"></i> Login
                    </a>
                    <a href="<?php echo BASE_URL; ?>?page=register" class="btn btn-light text-primary">
                        <i class="fas fa-user-plus me-1"></i> Sign Up
                    </a>
                </div>
            </div>
        </div>
    </nav>
    <?php elseif (isLoggedIn()): ?>
    <!-- Logged-in User Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                <?php echo getSetting('app_name', 'TimeTracker Pro'); ?>
                <?php if (isset($_SESSION['company_name'])): ?>
                    <span class="badge bg-light text-primary ms-2"><?php echo $_SESSION['company_name']; ?></span>
                <?php endif; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <?php if (isSuperAdmin() && !isset($_SESSION['company_id'])): ?>
                    <!-- Superadmin Navigation -->
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'superadmin' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>?page=superadmin">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'superadmin-companies' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>?page=superadmin-companies">
                                <i class="fas fa-building"></i> Companies
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'superadmin-users' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>?page=superadmin-users">
                                <i class="fas fa-users"></i> Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'superadmin-subscriptions' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>?page=superadmin-subscriptions">
                                <i class="fas fa-credit-card"></i> Subscriptions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'superadmin-settings' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>?page=superadmin-settings">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                        </li>
                    </ul>
                <?php else: ?>
                    <!-- Regular User Navigation -->
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'dashboard' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>?page=dashboard">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'timesheet' || $page === 'timesheet-daily' || $page === 'timesheet-weekly' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>?page=timesheet">
                                <i class="fas fa-clock"></i> Timesheet
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'projects' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>?page=projects">
                                <i class="fas fa-project-diagram"></i> Projects
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $page === 'reports' ? 'active' : ''; ?>" href="<?php echo BASE_URL; ?>?page=reports">
                                <i class="fas fa-chart-bar"></i> Reports
                            </a>
                        </li>
                        <?php if (isAdmin() && isset($_SESSION['company_id'])): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo strpos($page, 'admin') === 0 ? 'active' : ''; ?>" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-cog"></i> Admin
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                                <li>
                                    <a class="dropdown-item" href="<?php echo BASE_URL; ?>?page=admin">
                                        <i class="fas fa-tachometer-alt"></i> Dashboard
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo BASE_URL; ?>?page=admin-users">
                                        <i class="fas fa-users"></i> Users
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo BASE_URL; ?>?page=admin-projects">
                                        <i class="fas fa-project-diagram"></i> Projects
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo BASE_URL; ?>?page=admin-timesheets">
                                        <i class="fas fa-clock"></i> Timesheets
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo BASE_URL; ?>?page=admin-features">
                                        <i class="fas fa-star"></i> Subscription Features
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo BASE_URL; ?>?page=admin-settings">
                                        <i class="fas fa-sliders-h"></i> Settings
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <?php endif; ?>
                    </ul>
                <?php endif; ?>

                <ul class="navbar-nav">
                    <?php if (isset($_SESSION['superadmin_id'])): ?>
                        <li class="nav-item me-3">
                            <a class="btn btn-outline-light btn-sm" href="<?php echo BASE_URL; ?>?page=return-to-superadmin">
                                <i class="fas fa-arrow-left"></i> Return to Super Admin
                            </a>
                        </li>
                    <?php endif; ?>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user"></i> <?php echo $_SESSION['user_full_name']; ?>
                            <?php if (isSuperAdmin()): ?>
                                <span class="badge bg-danger">Super Admin</span>
                            <?php endif; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li>
                                <a class="dropdown-item" href="<?php echo BASE_URL; ?>?page=profile">
                                    <i class="fas fa-id-card"></i> Profile
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="<?php echo BASE_URL; ?>?page=logout">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="py-4">
        <div class="container">
