<?php
// Check if user is admin
if (!isAdmin()) {
    header('Location: ' . BASE_URL . '?page=dashboard');
    exit;
}

// Get user counts
$stmt = $pdo->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
$userCounts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

$adminCount = $userCounts['admin'] ?? 0;
$staffCount = $userCounts['staff'] ?? 0;
$contractorCount = $userCounts['contractor'] ?? 0;
$totalUsers = $adminCount + $staffCount + $contractorCount;

// Get timesheet counts
$stmt = $pdo->query("SELECT status, COUNT(*) as count FROM timesheets GROUP BY status");
$timesheetCounts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

$pendingCount = $timesheetCounts['pending'] ?? 0;
$approvedCount = $timesheetCounts['approved'] ?? 0;
$rejectedCount = $timesheetCounts['rejected'] ?? 0;
$totalTimesheets = $pendingCount + $approvedCount + $rejectedCount;

// Get project counts
$stmt = $pdo->query("SELECT status, COUNT(*) as count FROM projects GROUP BY status");
$projectCounts = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

$activeProjects = $projectCounts['active'] ?? 0;
$completedProjects = $projectCounts['completed'] ?? 0;
$onHoldProjects = $projectCounts['on-hold'] ?? 0;
$totalProjects = $activeProjects + $completedProjects + $onHoldProjects;

// Get recent users
$stmt = $pdo->query("SELECT * FROM users ORDER BY created_at DESC LIMIT 5");
$recentUsers = $stmt->fetchAll();

// Get timesheet mode
$timesheetMode = getTimesheetMode();
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3">Admin Dashboard</h1>
        <p class="text-muted">System overview and management</p>
    </div>
    <div class="col-md-6 text-md-end">
        <a href="<?php echo BASE_URL; ?>?page=admin-settings" class="btn btn-primary">
            <i class="fas fa-cog"></i> System Settings
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card h-100 border-primary">
            <div class="card-body">
                <h5 class="card-title">Users</h5>
                <p class="display-4"><?php echo $totalUsers; ?></p>
                <div class="d-flex justify-content-between">
                    <span class="text-muted">Admins: <?php echo $adminCount; ?></span>
                    <span class="text-muted">Staff: <?php echo $staffCount; ?></span>
                    <span class="text-muted">Contractors: <?php echo $contractorCount; ?></span>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="<?php echo BASE_URL; ?>?page=admin-users" class="btn btn-sm btn-outline-primary w-100">
                    Manage Users
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card h-100 border-success">
            <div class="card-body">
                <h5 class="card-title">Timesheets</h5>
                <p class="display-4"><?php echo $totalTimesheets; ?></p>
                <div class="d-flex justify-content-between">
                    <span class="text-muted">Pending: <?php echo $pendingCount; ?></span>
                    <span class="text-muted">Approved: <?php echo $approvedCount; ?></span>
                    <span class="text-muted">Rejected: <?php echo $rejectedCount; ?></span>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="<?php echo BASE_URL; ?>?page=admin-timesheets" class="btn btn-sm btn-outline-success w-100">
                    Manage Timesheets
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card h-100 border-info">
            <div class="card-body">
                <h5 class="card-title">Projects</h5>
                <p class="display-4"><?php echo $totalProjects; ?></p>
                <div class="d-flex justify-content-between">
                    <span class="text-muted">Active: <?php echo $activeProjects; ?></span>
                    <span class="text-muted">Completed: <?php echo $completedProjects; ?></span>
                    <span class="text-muted">On Hold: <?php echo $onHoldProjects; ?></span>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="<?php echo BASE_URL; ?>?page=admin-projects" class="btn btn-sm btn-outline-info w-100">
                    Manage Projects
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card h-100 border-warning">
            <div class="card-body">
                <h5 class="card-title">System Settings</h5>
                <p class="h5 mb-3">Timesheet Mode:</p>
                <p class="h4 text-capitalize"><?php echo $timesheetMode; ?></p>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="<?php echo BASE_URL; ?>?page=admin-settings" class="btn btn-sm btn-outline-warning w-100">
                    Manage Settings
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3 mb-4">
        <?php
        // Get company subscription
        $stmt = $pdo->prepare("
            SELECT cs.*, sp.name as plan_name, sp.price as plan_price
            FROM company_subscriptions cs
            JOIN subscription_plans sp ON cs.plan_id = sp.id
            WHERE cs.company_id = ? AND cs.status = 'active'
            ORDER BY cs.end_date DESC
            LIMIT 1
        ");
        $stmt->execute([$companyId]);
        $subscription = $stmt->fetch();
        ?>
        <div class="card h-100 border-info">
            <div class="card-body">
                <h5 class="card-title">Subscription</h5>
                <?php if ($subscription): ?>
                    <p class="h5 mb-2">Plan: <span class="badge bg-info"><?php echo $subscription['plan_name']; ?></span></p>
                    <p class="h6 mb-2">Price: $<?php echo number_format($subscription['plan_price'], 2); ?>/month</p>
                    <p class="small text-muted">
                        Valid until: <?php echo formatDate($subscription['end_date'], 'M d, Y'); ?>
                    </p>
                <?php else: ?>
                    <p class="text-danger">No active subscription</p>
                <?php endif; ?>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="<?php echo BASE_URL; ?>?page=admin-features" class="btn btn-sm btn-outline-info w-100">
                    View Features
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">Recent Users</h5>
            </div>
            <div class="card-body">
                <?php if (empty($recentUsers)): ?>
                    <p class="text-muted">No users found.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Username</th>
                                    <th>Role</th>
                                    <th>Joined</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentUsers as $user): ?>
                                    <tr>
                                        <td><?php echo $user['first_name'] . ' ' . $user['last_name']; ?></td>
                                        <td><?php echo $user['username']; ?></td>
                                        <td>
                                            <?php if ($user['role'] === 'admin'): ?>
                                                <span class="badge bg-danger">Admin</span>
                                            <?php elseif ($user['role'] === 'staff'): ?>
                                                <span class="badge bg-primary">Staff</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Contractor</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo formatDate($user['created_at'], 'M d, Y'); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-end mt-3">
                        <a href="<?php echo BASE_URL; ?>?page=admin-users" class="btn btn-link">View All Users</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    <a href="<?php echo BASE_URL; ?>?page=admin-users" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1"><i class="fas fa-users"></i> Manage Users</h5>
                        </div>
                        <p class="mb-1">Add, edit, or deactivate user accounts</p>
                    </a>

                    <a href="<?php echo BASE_URL; ?>?page=admin-projects" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1"><i class="fas fa-project-diagram"></i> Manage Projects</h5>
                        </div>
                        <p class="mb-1">Create and manage projects for time tracking</p>
                    </a>

                    <a href="<?php echo BASE_URL; ?>?page=admin-timesheets" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1"><i class="fas fa-clock"></i> Review Timesheets</h5>
                        </div>
                        <p class="mb-1">Approve or reject pending timesheet entries</p>
                    </a>

                    <a href="<?php echo BASE_URL; ?>?page=admin-features" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1"><i class="fas fa-star"></i> Subscription Features</h5>
                        </div>
                        <p class="mb-1">View your subscription plan and available features</p>
                    </a>

                    <a href="<?php echo BASE_URL; ?>?page=admin-settings" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1"><i class="fas fa-cog"></i> System Settings</h5>
                        </div>
                        <p class="mb-1">Configure application settings and preferences</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
