<?php
// Check if registration is allowed
$allowStaffRegistration = getSetting('allow_staff_registration', '1') == '1';
$allowContractorRegistration = getSetting('allow_contractor_registration', '1') == '1';

if (!$allowStaffRegistration && !$allowContractorRegistration) {
    echo '<div class="alert alert-danger">Registration is currently disabled.</div>';
    exit;
}

// Process registration form
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $userData = [
        'username' => $_POST['username'] ?? '',
        'email' => $_POST['email'] ?? '',
        'password' => $_POST['password'] ?? '',
        'confirm_password' => $_POST['confirm_password'] ?? '',
        'first_name' => $_POST['first_name'] ?? '',
        'last_name' => $_POST['last_name'] ?? '',
        'role' => $_POST['role'] ?? 'staff'
    ];

    // Validate form data
    if (empty($userData['username']) || empty($userData['email']) || empty($userData['password']) ||
        empty($userData['confirm_password']) || empty($userData['first_name']) || empty($userData['last_name'])) {
        $error = "All fields are required";
    } elseif ($userData['password'] !== $userData['confirm_password']) {
        $error = "Passwords do not match";
    } elseif (!filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } elseif (strlen($userData['password']) < 6) {
        $error = "Password must be at least 6 characters long";
    } elseif (!in_array($userData['role'], ['staff', 'contractor'])) {
        $error = "Invalid role selected";
    } elseif ($userData['role'] === 'staff' && !$allowStaffRegistration) {
        $error = "Staff registration is currently disabled";
    } elseif ($userData['role'] === 'contractor' && !$allowContractorRegistration) {
        $error = "Contractor registration is currently disabled";
    } else {
        // Register user
        $result = registerUser($userData);

        if (is_numeric($result)) {
            $success = "Registration successful! You can now <a href='" . BASE_URL . "?page=login'>login</a>.";
        } else {
            $error = $result;
        }
    }
}
?>

<div class="back-to-home">
    <a href="<?php echo BASE_URL; ?>"><i class="fas fa-arrow-left"></i> Back to Home</a>
</div>

<div class="auth-card">
    <div class="card-header">
        <h4 class="mb-0">Create Your Account</h4>
    </div>
    <div class="card-body">
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
            <div class="text-center mt-4">
                <a href="<?php echo BASE_URL; ?>?page=login" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i> Go to Login
                </a>
            </div>
        <?php else: ?>
            <form method="post">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="first_name" class="form-label">First Name</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="first_name" name="first_name" placeholder="Enter first name" required>
                        </div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="last_name" class="form-label">Last Name</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" class="form-control" id="last_name" name="last_name" placeholder="Enter last name" required>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-at"></i></span>
                        <input type="text" class="form-control" id="username" name="username" placeholder="Choose a username" required>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="email" class="form-label">Email</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                        <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email" required>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="password" name="password" placeholder="Create password" required>
                        </div>
                        <div class="form-text">Password must be at least 6 characters long.</div>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="confirm_password" class="form-label">Confirm Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Confirm password" required>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <label class="form-label">Register as</label>
                    <div class="d-flex gap-3">
                        <?php if ($allowStaffRegistration): ?>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="role" id="role_staff" value="staff" checked>
                                <label class="form-check-label" for="role_staff">
                                    <i class="fas fa-user-tie me-1"></i> Staff
                                </label>
                            </div>
                        <?php endif; ?>

                        <?php if ($allowContractorRegistration): ?>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="role" id="role_contractor" value="contractor" <?php echo !$allowStaffRegistration ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="role_contractor">
                                    <i class="fas fa-briefcase me-1"></i> Contractor
                                </label>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="d-grid gap-2 mb-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i> Create Account
                    </button>
                </div>
            </form>

            <div class="text-center">
                <p class="mb-0">
                    Already have an account? <a href="<?php echo BASE_URL; ?>?page=login" class="fw-bold"><i class="fas fa-sign-in-alt me-1"></i> Login</a>
                </p>
            </div>
        <?php endif; ?>
    </div>
</div>
