/**
 * Theme Switcher for TimeTracker
 * Allows users to toggle between original and alternative designs
 */

document.addEventListener('DOMContentLoaded', function() {
    // Function to set theme preference in localStorage
    function setThemePreference(theme) {
        localStorage.setItem('timeTracker.theme', theme);
    }

    // Function to get theme preference from localStorage
    function getThemePreference() {
        return localStorage.getItem('timeTracker.theme') || 'alternative';
    }// Function to redirect to the appropriate version based on the current page
    function switchTheme(theme) {
        // Get current URL and page parameter
        const urlParams = new URLSearchParams(window.location.search);
        const currentPage = urlParams.get('page') || 'landing';
        
        console.log('Switching theme to:', theme);
        console.log('Current page:', currentPage);
        console.log('Current URL:', window.location.href);
          // Determine if we're on an auth page
        const isAuthPage = ['login', 'register', 'forgot-password', 'reset-password', 
                           'login-alternative', 'register-alternative', 
                           'forgot-password-alternative', 'reset-password-alternative'].includes(currentPage);
        
        // Check if we're on a special page that doesn't need theme switching
        const isDesignComparisonPage = currentPage === 'design-comparison' || 
                                     window.location.pathname.includes('design-comparison.php') ||
                                     window.location.pathname.includes('comparison.php');
        
        // Don't switch theme on the comparison page
        if (isDesignComparisonPage) {
            console.log('On design comparison page, not switching theme');
            return;
        }
        
        // Check if we're on a standalone page
        const isStandalonePage = window.location.pathname.includes('-alternative.php');
        console.log('Is standalone page:', isStandalonePage);
        
        // Determine target URL
        let targetUrl;
          if (theme === 'alternative') {
            if (currentPage === 'landing' || !currentPage) {
                targetUrl = BASE_URL + 'landing-alternative.php';
            } else if (isAuthPage) {
                // For auth pages, redirect to the alternative version
                const basePage = currentPage.replace('-alternative', '');
                targetUrl = `${BASE_URL}?page=${basePage}-alternative`;
                
                // Handle token parameter for reset password
                if (basePage === 'reset-password' || basePage === 'reset-password-alternative') {
                    const token = urlParams.get('token');
                    if (token) {
                        targetUrl += `&token=${token}`;
                    }
                }
            } else if (window.location.pathname.includes('login.php')) {
                targetUrl = BASE_URL + 'login-alternative.php';
            } else if (window.location.pathname.includes('register.php')) {
                targetUrl = BASE_URL + 'register-alternative.php';
            } else {
                // For other pages (dashboard, etc.), they don't have alternative versions yet
                // Just apply alternative styling if available
                document.documentElement.classList.add('theme-alternative');
                return;
            }
        } else {
            // Original theme
            if (currentPage === 'landing-alternative') {
                targetUrl = BASE_URL;
            } else if (isAuthPage) {
                // For auth pages, redirect to the original version
                const basePage = currentPage.replace('-alternative', '');
                targetUrl = `${BASE_URL}?page=${basePage}`;
                
                // Handle token parameter for reset password
                if (basePage === 'reset-password' || basePage === 'reset-password-alternative') {
                    const token = urlParams.get('token');
                    if (token) {
                        targetUrl += `&token=${token}`;
                    }
                }
            } else if (window.location.pathname.includes('login-alternative.php')) {
                targetUrl = BASE_URL + '?page=login';
            } else if (window.location.pathname.includes('register-alternative.php')) {
                targetUrl = BASE_URL + '?page=register';
            } else if (window.location.pathname.includes('landing-alternative.php')) {
                targetUrl = BASE_URL;
            } else {
                // For other pages, remove alternative styling if applied
                document.documentElement.classList.remove('theme-alternative');
                return;
            }
        }
        
        // Redirect to the appropriate URL
        window.location.href = targetUrl;
    }

    // Add theme toggle button to the page
    function addThemeToggleButton() {
        const currentTheme = getThemePreference();
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'theme-toggle-container';
        
        const toggleButton = document.createElement('button');
        toggleButton.type = 'button';
        toggleButton.className = 'theme-toggle-button';
        toggleButton.setAttribute('aria-label', 'Toggle theme');
        toggleButton.innerHTML = currentTheme === 'alternative' 
            ? '<i class="fas fa-sun"></i>' 
            : '<i class="fas fa-moon"></i>';
        
        toggleButton.addEventListener('click', function() {
            const newTheme = currentTheme === 'alternative' ? 'original' : 'alternative';
            setThemePreference(newTheme);
            switchTheme(newTheme);
        });
        
        buttonContainer.appendChild(toggleButton);
        document.body.appendChild(buttonContainer);
        
        // Add styles for theme toggle button
        const style = document.createElement('style');
        style.textContent = `
            .theme-toggle-container {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1000;
            }
            
            .theme-toggle-button {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: ${currentTheme === 'alternative' ? 'var(--primary-alt, #4f46e5)' : 'var(--primary-color, #4361ee)'};
                color: white;
                border: none;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 20px;
            }
            
            .theme-toggle-button:hover {
                transform: translateY(-5px);
                box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
            }
            
            @media (max-width: 768px) {
                .theme-toggle-container {
                    bottom: 15px;
                    right: 15px;
                }
                
                .theme-toggle-button {
                    width: 40px;
                    height: 40px;
                    font-size: 16px;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Initialize theme toggle button
    addThemeToggleButton();
    
    // Apply theme on page load
    const savedTheme = getThemePreference();
    if (savedTheme === 'alternative') {
        document.documentElement.classList.add('theme-alternative');
    }
});
