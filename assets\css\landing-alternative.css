/* Landing Page Alternative Styles */

/* Base Variables */
:root {
    --primary-alt: #3b82f6;
    --primary-dark-alt: #2563eb;
    --primary-light-alt: #60a5fa;
    --secondary-alt: #10b981;
    --accent-alt: #6366f1;
    --dark-alt: #1f2937;
    --light-alt: #f8fafc;
    --text-alt: #111827;
    --text-light-alt: #ffffff;
    --text-muted-alt: #6b7280;
    --gradient-alt: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-soft: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-blue: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --shadow-sm-alt: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md-alt: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg-alt: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --border-radius-alt: 1rem;
    --transition-alt: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hero Section Styles */
.hero-section-alt {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--text-light-alt);
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 8rem 0 4rem;
}

.hero-section-alt::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    z-index: 1;
}

.hero-section-alt::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: url("data:image/svg+xml,%3Csvg viewBox='0 0 1200 120' preserveAspectRatio='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%23ffffff'/%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' fill='%23ffffff'/%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' fill='%23ffffff'/%3E%3C/svg%3E");
    background-size: cover;
    z-index: 2;
}

.hero-text-alt {
    position: relative;
    z-index: 3;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 1.6;
    margin-bottom: 2rem;
    opacity: 0.9;
    color: #f1f5f9;
}

/* Button Improvements */
.btn-light {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: none;
    color: var(--primary-alt);
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn-light:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(0,0,0,0.2);
    color: var(--primary-dark-alt);
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.8);
    color: white;
    background: transparent;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255,255,255,0.2);
}

.shine-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.shine-btn:hover::before {
    left: 100%;
}

.hero-tagline {
    position: relative;
    display: inline-block;
}

.hero-tagline::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.shine-btn {
    position: relative;
    overflow: hidden;
}

.shine-btn::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(60deg, rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0) 80%);
    transform: rotate(45deg);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% {
        left: -100%;
        opacity: 0;
    }
    20% {
        left: 100%;
        opacity: 0.6;
    }
    100% {
        left: 100%;
        opacity: 0;
    }
}

.dashboard-preview {
    position: relative;
    z-index: 1;
    transform: perspective(1000px) rotateY(-15deg) rotateX(5deg) rotate(2deg);
    transition: var(--transition-alt);
}

.dashboard-preview:hover {
    transform: perspective(1000px) rotateY(-5deg) rotateX(2deg) rotate(1deg);
}

.browser-mockup {
    position: relative;
    border-radius: 6px 6px 0 0;
    box-shadow: var(--shadow-lg-alt);
    overflow: hidden;
}

.browser-mockup::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 28px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    border-radius: 6px 6px 0 0;
    z-index: 1;
}

.browser-mockup::after {
    content: '';
    position: absolute;
    top: 9px;
    left: 12px;
    width: 10px;
    height: 10px;
    background: #f87171;
    border-radius: 50%;
    box-shadow: 18px 0 0 #fbbf24, 36px 0 0 #34d399;
    z-index: 2;
}

.browser-mockup img {
    margin-top: 28px;
}

.wave-divider {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
}

/* Trusted Section */
.trusted-section {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

/* Features Section */
.features-section-alt {
    padding: 5rem 0;
}

.feature-card-alt {
    background-color: #fff;
    border-radius: var(--border-radius-alt);
    padding: 2rem;
    height: 100%;
    box-shadow: var(--shadow-sm-alt);
    transition: var(--transition-alt);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card-alt:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md-alt);
}

.feature-icon-alt {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

/* How It Works Section */
.how-it-works-section-alt {
    position: relative;
    padding: 5rem 0;
    background-color: #fff;
}

.step-container {
    position: relative;
    padding-left: 40px;
}

.step-timeline {
    position: absolute;
    top: 0;
    left: 18px;
    height: 100%;
    width: 4px;
    background-color: var(--primary-light-alt);
    border-radius: 2px;
}

.step-item-alt {
    position: relative;
    padding-bottom: 2rem;
}

.step-item-alt:last-child {
    padding-bottom: 0;
}

.step-number-alt {
    position: absolute;
    left: -40px;
    width: 40px;
    height: 40px;
    background: var(--primary-alt);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.25rem;
    z-index: 1;
    box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.2);
}

.step-content-alt {
    background-color: #fff;
    border-radius: var(--border-radius-alt);
    padding: 1.5rem;
    margin-left: 1rem;
    box-shadow: var(--shadow-sm-alt);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.timesheet-preview {
    position: relative;
    z-index: 1;
    box-shadow: var(--shadow-lg-alt);
    border-radius: 6px;
    overflow: hidden;
}

/* Testimonials Section */
.testimonials-section-alt {
    padding: 5rem 0;
    background-color: var(--light-alt);
}

.testimonial-card-alt {
    background-color: #fff;
    border-radius: var(--border-radius-alt);
    padding: 2rem;
    height: 100%;
    box-shadow: var(--shadow-sm-alt);
    transition: var(--transition-alt);
    position: relative;
    overflow: hidden;
}

.testimonial-card-alt:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md-alt);
}

.testimonial-quote {
    color: var(--primary-light-alt);
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.testimonial-content-alt {
    margin-bottom: 1.5rem;
    font-style: italic;
    color: var(--text-alt);
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.author-image {
    margin-right: 1rem;
}

.author-image img {
    border: 3px solid var(--primary-light-alt);
}

/* Pricing Section */
.pricing-section-alt {
    padding: 5rem 0;
}

.pricing-card-alt {
    background-color: #fff;
    border-radius: var(--border-radius-alt);
    overflow: hidden;
    height: 100%;
    box-shadow: var(--shadow-sm-alt);
    transition: var(--transition-alt);
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.pricing-card-alt:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md-alt);
}

.pricing-popular-alt {
    border: 2px solid var(--primary-alt);
    transform: scale(1.05);
    z-index: 1;
}

.pricing-popular-alt:hover {
    transform: translateY(-10px) scale(1.05);
}

.pricing-badge-alt {
    position: absolute;
    top: 0;
    right: 2rem;
    background: var(--primary-alt);
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: bold;
    border-radius: 0 0 0.5rem 0.5rem;
    z-index: 2;
}

.pricing-header-alt {
    padding: 2rem;
    text-align: center;
}

.pricing-divider {
    height: 1px;
    background: rgba(0, 0, 0, 0.05);
    margin: 0 2rem;
}

.pricing-price-alt {
    font-size: 3rem;
    font-weight: bold;
    color: var(--primary-alt);
    line-height: 1;
    margin: 1rem 0;
}

.pricing-price-alt .currency {
    font-size: 1.5rem;
    vertical-align: super;
    font-weight: normal;
    margin-right: 0.25rem;
}

.pricing-price-alt .period {
    font-size: 1rem;
    color: var(--text-muted-alt);
    font-weight: normal;
}

.pricing-features-alt {
    padding: 2rem;
}

.pricing-features-alt ul li {
    margin-bottom: 0.75rem;
}

.pricing-footer-alt {
    padding: 2rem;
    text-align: center;
}

/* CTA Section */
.cta-section-alt {
    background: var(--gradient-alt);
    color: white;
    padding: 5rem 0;
}

.cta-card-alt {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-alt);
    padding: 3rem 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Newsletter Section */
.newsletter-section-alt {
    padding: 5rem 0;
    background-color: #fff;
}

.newsletter-form-alt {
    max-width: 600px;
    margin: 0 auto;
}

/* Responsive Styles */
@media (max-width: 991.98px) {
    .hero-section-alt {
        padding-top: 8rem;
        padding-bottom: 4rem;
        min-height: auto;
    }
    
    .pricing-popular-alt {
        transform: scale(1);
    }
    
    .pricing-popular-alt:hover {
        transform: translateY(-10px) scale(1);
    }
}

@media (max-width: 767.98px) {
    .feature-card-alt,
    .testimonial-card-alt,
    .pricing-card-alt {
        margin-bottom: 1.5rem;
    }
    
    .step-container {
        margin-bottom: 2rem;
    }
    
    .cta-card-alt {
        padding: 2rem 1rem;
    }
}

/* Animation Styles */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.animated-alt {
    opacity: 1;
    transform: translateY(0);
}

.feature-card-alt.animate-on-scroll {
    transform: translateY(30px) scale(0.95);
}

.feature-card-alt.animated-alt {
    transform: translateY(0) scale(1);
}

.testimonial-card-alt.animate-on-scroll {
    transform: translateY(30px) rotate(-1deg);
}

.testimonial-card-alt.animated-alt {
    transform: translateY(0) rotate(0);
}

.pricing-card-alt.animate-on-scroll {
    transform: translateY(30px);
}

.pricing-card-alt.animated-alt {
    transform: translateY(0);
}

.pricing-popular-alt.animate-on-scroll {
    transform: translateY(30px) scale(1.05);
}

.pricing-popular-alt.animated-alt {
    transform: translateY(0) scale(1.05);
}

/* Footer Styles */
.footer-alt {
    background-color: #f9fafb;
    color: var(--text-alt);
}

.footer-links a {
    color: var(--text-muted-alt);
    text-decoration: none;
    transition: var(--transition-alt);
    display: inline-block;
    margin-bottom: 0.5rem;
}

.footer-links a:hover {
    color: var(--primary-alt);
    transform: translateX(3px);
}

.social-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(79, 70, 229, 0.1);
    color: var(--primary-alt);
    transition: var(--transition-alt);
}

.social-icon:hover {
    background-color: var(--primary-alt);
    color: white;
    transform: translateY(-3px);
}

/* Responsive Navbar */
.navbar-alt {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.navbar-transparent-alt {
    background-color: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-scrolled-alt {
    background-color: rgba(255, 255, 255, 0.95) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding-top: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.navbar-scrolled-alt .navbar-brand,
.navbar-scrolled-alt .nav-link {
    color: var(--text-alt) !important;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
    text-decoration: none;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
}

.nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-section-alt {
        padding: 6rem 0 3rem;
    }

    .btn-lg {
        padding: 0.75rem 2rem;
        font-size: 1rem;
    }
}

@media (max-width: 991.98px) {
    .navbar-collapse-alt {
        background-color: rgba(255, 255, 255, 0.95);
        border-radius: var(--border-radius-alt);
        padding: 1rem;
        margin-top: 0.5rem;
        box-shadow: var(--shadow-md-alt);
        max-height: 85vh;
        overflow-y: auto;
        backdrop-filter: blur(10px);
    }

    .navbar-nav-alt .nav-link {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        color: var(--text-alt) !important;
    }

    .navbar-nav-alt .nav-item:last-child .nav-link {
        border-bottom: none;
    }
}
