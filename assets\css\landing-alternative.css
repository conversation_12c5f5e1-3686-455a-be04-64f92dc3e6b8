/* Landing Page Alternative Styles */

/* Base Variables */
:root {
    --primary-alt: #4f46e5;
    --primary-dark-alt: #4338ca;
    --primary-light-alt: #818cf8;
    --secondary-alt: #10b981;
    --accent-alt: #8b5cf6;
    --dark-alt: #1f2937;
    --light-alt: #f9fafb;
    --text-alt: #111827;
    --text-light-alt: #f8fafc;
    --text-muted-alt: #6b7280;
    --gradient-alt: linear-gradient(135deg, var(--primary-alt), var(--accent-alt));
    --shadow-sm-alt: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md-alt: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg-alt: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --border-radius-alt: 0.75rem;
    --transition-alt: all 0.3s ease;
}

/* Hero Section Styles */
.hero-section-alt {
    position: relative;
    overflow: hidden;
    background: var(--gradient-alt);
    color: var(--text-light-alt);
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 6rem 0 2rem;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.8;
}

.hero-text-alt {
    position: relative;
    z-index: 1;
}

.hero-tagline {
    position: relative;
    display: inline-block;
}

.hero-tagline::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.shine-btn {
    position: relative;
    overflow: hidden;
}

.shine-btn::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(60deg, rgba(255, 255, 255, 0) 20%, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0) 80%);
    transform: rotate(45deg);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% {
        left: -100%;
        opacity: 0;
    }
    20% {
        left: 100%;
        opacity: 0.6;
    }
    100% {
        left: 100%;
        opacity: 0;
    }
}

.dashboard-preview {
    position: relative;
    z-index: 1;
    transform: perspective(1000px) rotateY(-15deg) rotateX(5deg) rotate(2deg);
    transition: var(--transition-alt);
}

.dashboard-preview:hover {
    transform: perspective(1000px) rotateY(-5deg) rotateX(2deg) rotate(1deg);
}

.browser-mockup {
    position: relative;
    border-radius: 6px 6px 0 0;
    box-shadow: var(--shadow-lg-alt);
    overflow: hidden;
}

.browser-mockup::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 28px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    border-radius: 6px 6px 0 0;
    z-index: 1;
}

.browser-mockup::after {
    content: '';
    position: absolute;
    top: 9px;
    left: 12px;
    width: 10px;
    height: 10px;
    background: #f87171;
    border-radius: 50%;
    box-shadow: 18px 0 0 #fbbf24, 36px 0 0 #34d399;
    z-index: 2;
}

.browser-mockup img {
    margin-top: 28px;
}

.wave-divider {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
}

/* Trusted Section */
.trusted-section {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

/* Features Section */
.features-section-alt {
    padding: 5rem 0;
}

.feature-card-alt {
    background-color: #fff;
    border-radius: var(--border-radius-alt);
    padding: 2rem;
    height: 100%;
    box-shadow: var(--shadow-sm-alt);
    transition: var(--transition-alt);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card-alt:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md-alt);
}

.feature-icon-alt {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

/* How It Works Section */
.how-it-works-section-alt {
    position: relative;
    padding: 5rem 0;
    background-color: #fff;
}

.step-container {
    position: relative;
    padding-left: 40px;
}

.step-timeline {
    position: absolute;
    top: 0;
    left: 18px;
    height: 100%;
    width: 4px;
    background-color: var(--primary-light-alt);
    border-radius: 2px;
}

.step-item-alt {
    position: relative;
    padding-bottom: 2rem;
}

.step-item-alt:last-child {
    padding-bottom: 0;
}

.step-number-alt {
    position: absolute;
    left: -40px;
    width: 40px;
    height: 40px;
    background: var(--primary-alt);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.25rem;
    z-index: 1;
    box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.2);
}

.step-content-alt {
    background-color: #fff;
    border-radius: var(--border-radius-alt);
    padding: 1.5rem;
    margin-left: 1rem;
    box-shadow: var(--shadow-sm-alt);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.timesheet-preview {
    position: relative;
    z-index: 1;
    box-shadow: var(--shadow-lg-alt);
    border-radius: 6px;
    overflow: hidden;
}

/* Testimonials Section */
.testimonials-section-alt {
    padding: 5rem 0;
    background-color: var(--light-alt);
}

.testimonial-card-alt {
    background-color: #fff;
    border-radius: var(--border-radius-alt);
    padding: 2rem;
    height: 100%;
    box-shadow: var(--shadow-sm-alt);
    transition: var(--transition-alt);
    position: relative;
    overflow: hidden;
}

.testimonial-card-alt:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md-alt);
}

.testimonial-quote {
    color: var(--primary-light-alt);
    font-size: 2rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.testimonial-content-alt {
    margin-bottom: 1.5rem;
    font-style: italic;
    color: var(--text-alt);
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.author-image {
    margin-right: 1rem;
}

.author-image img {
    border: 3px solid var(--primary-light-alt);
}

/* Pricing Section */
.pricing-section-alt {
    padding: 5rem 0;
}

.pricing-card-alt {
    background-color: #fff;
    border-radius: var(--border-radius-alt);
    overflow: hidden;
    height: 100%;
    box-shadow: var(--shadow-sm-alt);
    transition: var(--transition-alt);
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.pricing-card-alt:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md-alt);
}

.pricing-popular-alt {
    border: 2px solid var(--primary-alt);
    transform: scale(1.05);
    z-index: 1;
}

.pricing-popular-alt:hover {
    transform: translateY(-10px) scale(1.05);
}

.pricing-badge-alt {
    position: absolute;
    top: 0;
    right: 2rem;
    background: var(--primary-alt);
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: bold;
    border-radius: 0 0 0.5rem 0.5rem;
    z-index: 2;
}

.pricing-header-alt {
    padding: 2rem;
    text-align: center;
}

.pricing-divider {
    height: 1px;
    background: rgba(0, 0, 0, 0.05);
    margin: 0 2rem;
}

.pricing-price-alt {
    font-size: 3rem;
    font-weight: bold;
    color: var(--primary-alt);
    line-height: 1;
    margin: 1rem 0;
}

.pricing-price-alt .currency {
    font-size: 1.5rem;
    vertical-align: super;
    font-weight: normal;
    margin-right: 0.25rem;
}

.pricing-price-alt .period {
    font-size: 1rem;
    color: var(--text-muted-alt);
    font-weight: normal;
}

.pricing-features-alt {
    padding: 2rem;
}

.pricing-features-alt ul li {
    margin-bottom: 0.75rem;
}

.pricing-footer-alt {
    padding: 2rem;
    text-align: center;
}

/* CTA Section */
.cta-section-alt {
    background: var(--gradient-alt);
    color: white;
    padding: 5rem 0;
}

.cta-card-alt {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-alt);
    padding: 3rem 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Newsletter Section */
.newsletter-section-alt {
    padding: 5rem 0;
    background-color: #fff;
}

.newsletter-form-alt {
    max-width: 600px;
    margin: 0 auto;
}

/* Responsive Styles */
@media (max-width: 991.98px) {
    .hero-section-alt {
        padding-top: 8rem;
        padding-bottom: 4rem;
        min-height: auto;
    }
    
    .pricing-popular-alt {
        transform: scale(1);
    }
    
    .pricing-popular-alt:hover {
        transform: translateY(-10px) scale(1);
    }
}

@media (max-width: 767.98px) {
    .feature-card-alt,
    .testimonial-card-alt,
    .pricing-card-alt {
        margin-bottom: 1.5rem;
    }
    
    .step-container {
        margin-bottom: 2rem;
    }
    
    .cta-card-alt {
        padding: 2rem 1rem;
    }
}

/* Animation Styles */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.animated-alt {
    opacity: 1;
    transform: translateY(0);
}

.feature-card-alt.animate-on-scroll {
    transform: translateY(30px) scale(0.95);
}

.feature-card-alt.animated-alt {
    transform: translateY(0) scale(1);
}

.testimonial-card-alt.animate-on-scroll {
    transform: translateY(30px) rotate(-1deg);
}

.testimonial-card-alt.animated-alt {
    transform: translateY(0) rotate(0);
}

.pricing-card-alt.animate-on-scroll {
    transform: translateY(30px);
}

.pricing-card-alt.animated-alt {
    transform: translateY(0);
}

.pricing-popular-alt.animate-on-scroll {
    transform: translateY(30px) scale(1.05);
}

.pricing-popular-alt.animated-alt {
    transform: translateY(0) scale(1.05);
}

/* Footer Styles */
.footer-alt {
    background-color: #f9fafb;
    color: var(--text-alt);
}

.footer-links a {
    color: var(--text-muted-alt);
    text-decoration: none;
    transition: var(--transition-alt);
    display: inline-block;
    margin-bottom: 0.5rem;
}

.footer-links a:hover {
    color: var(--primary-alt);
    transform: translateX(3px);
}

.social-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(79, 70, 229, 0.1);
    color: var(--primary-alt);
    transition: var(--transition-alt);
}

.social-icon:hover {
    background-color: var(--primary-alt);
    color: white;
    transform: translateY(-3px);
}

/* Responsive Navbar */
.navbar-alt {
    transition: background-color 0.3s ease, padding 0.3s ease, box-shadow 0.3s ease;
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.navbar-transparent-alt {
    background-color: transparent;
}

.navbar-scrolled-alt {
    background-color: var(--primary-alt) !important;
    box-shadow: var(--shadow-md-alt);
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}

@media (max-width: 991.98px) {
    .navbar-collapse-alt {
        background-color: var(--primary-alt);
        border-radius: var(--border-radius-alt);
        padding: 1rem;
        margin-top: 0.5rem;
        box-shadow: var(--shadow-md-alt);
        max-height: 85vh;
        overflow-y: auto;
    }
    
    .navbar-nav-alt .nav-link {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .navbar-nav-alt .nav-item:last-child .nav-link {
        border-bottom: none;
    }
}

@media (max-width: 991.98px) {
    .navbar-collapse-alt {
        background-color: var(--primary-alt);
        border-radius: var(--border-radius-alt);
        padding: 1rem;
        margin-top: 0.5rem;
        box-shadow: var(--shadow-md-alt);
        max-height: 85vh;
        overflow-y: auto;
    }
    
    .navbar-nav-alt .nav-link {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .navbar-nav-alt .nav-item:last-child .nav-link {
        border-bottom: none;
    }
}
