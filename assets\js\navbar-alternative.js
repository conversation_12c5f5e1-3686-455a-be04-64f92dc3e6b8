/**
 * Navbar functionality for alternative design
 * Changes navbar background on scroll
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get the navbar element
    const navbar = document.getElementById('landingNavbarAlt');
    
    // Only proceed if we're on a page with the alternative navbar
    if (!navbar) return;
    
    // Change navbar background on scroll
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('navbar-scrolled-alt');
            navbar.classList.remove('navbar-transparent-alt');
        } else {
            if (!isLoggedIn()) {
                navbar.classList.remove('navbar-scrolled-alt');
                navbar.classList.add('navbar-transparent-alt');
            }
        }
    });
    
    // Helper function to check if user is logged in
    function isLoggedIn() {
        // We can check if the navbar has the bg-primary class
        // which is added in header-alternative.php when user is logged in
        return navbar.classList.contains('bg-primary');
    }
    
    // For dropdown menus on mobile
    const dropdowns = document.querySelectorAll('.dropdown-toggle');
    dropdowns.forEach(dropdown => {
        dropdown.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const dropdownMenu = this.nextElementSibling;
            dropdownMenu.classList.toggle('show');
        });
    });
});
