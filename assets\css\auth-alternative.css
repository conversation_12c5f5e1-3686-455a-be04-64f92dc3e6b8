/* Authentication Pages Alternative Styling - Matching Landing Alternative Design */

/* Base Variables - Matching landing-alternative.css variables */
:root {
    --primary-alt: #4f46e5;
    --primary-dark-alt: #4338ca;
    --primary-light-alt: #818cf8;
    --secondary-alt: #10b981;
    --accent-alt: #8b5cf6;
    --dark-alt: #1f2937;
    --light-alt: #f9fafb;
    --text-alt: #111827;
    --text-light-alt: #f8fafc;
    --text-muted-alt: #6b7280;
    --gradient-alt: linear-gradient(135deg, var(--primary-alt), var(--accent-alt));
    --shadow-sm-alt: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md-alt: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg-alt: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --border-radius-alt: 0.75rem;
    --transition-alt: all 0.3s ease;
}

/* Auth Page Container */
.auth-page-alt {
    min-height: 100vh;
    background: var(--light-alt);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 2rem 0;
}

/* Background Pattern */
.auth-page-alt::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%234f46e5' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
    z-index: 0;
}

/* Gradient Overlay */
.auth-page-alt::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 40%;
    background: var(--gradient-alt);
    z-index: -1;
    opacity: 0.6;
    clip-path: polygon(0 0, 100% 0, 100% 70%, 0 100%);
}

/* Auth Card Styles */
.auth-card-alt {
    position: relative;
    background-color: #fff;
    border-radius: var(--border-radius-alt);
    box-shadow: var(--shadow-lg-alt);
    overflow: hidden;
    max-width: 600px;
    margin: 0 auto;
    z-index: 1;
    border: 1px solid rgba(0, 0, 0, 0.05);
    animation: slideUpFade 0.6s ease-out;
}

@keyframes slideUpFade {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-header-alt {
    background-color: #fff;
    padding: 2rem 2.5rem 1.5rem;
    text-align: center;
    position: relative;
}

.auth-header-alt::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 10%;
    width: 80%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.auth-header-alt .brand {
    margin-bottom: 1.5rem;
}

.auth-header-alt h3 {
    color: var(--dark-alt);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.auth-header-alt p {
    color: var(--text-muted-alt);
    font-size: 0.95rem;
}

.auth-body-alt {
    padding: 2rem 2.5rem;
}

/* Form Styles */
.form-group-alt {
    margin-bottom: 1.75rem;
    position: relative;
}

.form-group-alt label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark-alt);
    font-size: 0.9rem;
}

.form-control-alt {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    background-color: #f8fafc;
    transition: var(--transition-alt);
    font-size: 1rem;
}

.form-control-alt:focus {
    outline: none;
    border-color: var(--primary-light-alt);
    background-color: #fff;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.input-group-alt {
    position: relative;
}

.input-group-alt .form-control-alt {
    padding-left: 3rem;
}

.input-group-icon {
    position: absolute;
    top: 50%;
    left: 1rem;
    transform: translateY(-50%);
    color: var(--primary-alt);
    opacity: 0.7;
    transition: var(--transition-alt);
}

.form-control-alt:focus + .input-group-icon {
    opacity: 1;
}

/* Button Styles */
.btn-alt {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.5rem;
    transition: var(--transition-alt);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-primary-alt {
    background-color: var(--primary-alt);
    color: white;
}

.btn-primary-alt:hover {
    background-color: var(--primary-dark-alt);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md-alt);
}

.btn-primary-alt:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm-alt);
}

.btn-outline-alt {
    background-color: transparent;
    color: var(--primary-alt);
    border: 1px solid var(--primary-alt);
}

.btn-outline-alt:hover {
    background-color: rgba(79, 70, 229, 0.08);
    transform: translateY(-2px);
}

.btn-outline-alt:active {
    transform: translateY(0);
}

.btn-block-alt {
    display: block;
    width: 100%;
}

/* Alert Styles */
.alert-alt {
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius-alt);
    font-size: 0.95rem;
}

.alert-success-alt {
    background-color: rgba(16, 185, 129, 0.1);
    color: #065f46;
    border-left: 3px solid #10b981;
}

.alert-danger-alt {
    background-color: rgba(239, 68, 68, 0.1);
    color: #b91c1c;
    border-left: 3px solid #ef4444;
}

/* Links */
.auth-footer-alt {
    text-align: center;
    padding: 0 2.5rem 2rem;
}

.auth-link {
    color: var(--primary-alt);
    text-decoration: none;
    transition: var(--transition-alt);
    position: relative;
    display: inline-block;
}

.auth-link:hover {
    color: var(--primary-dark-alt);
}

.auth-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-alt);
    transition: width 0.3s ease;
}

.auth-link:hover::after {
    width: 100%;
}

.auth-divider {
    display: flex;
    align-items: center;
    margin: 1.5rem 0;
    color: var(--text-muted-alt);
}

.auth-divider::before,
.auth-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: #e2e8f0;
}

.auth-divider span {
    padding: 0 1rem;
    font-size: 0.9rem;
}

/* Social Login Buttons */
.social-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.social-btn {
    flex: 1;
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    background-color: white;
    color: var(--text-alt);
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-alt);
}

.social-btn:hover {
    background-color: #f8fafc;
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm-alt);
}

.social-btn i {
    margin-right: 0.5rem;
}

.social-btn-google i {
    color: #ea4335;
}

.social-btn-facebook i {
    color: #1877f2;
}

/* Back to Home Link */
.back-to-home-alt {
    position: absolute;
    top: 2rem;
    left: 2rem;
    z-index: 10;
}

.back-to-home-alt a {
    display: flex;
    align-items: center;
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    background-color: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: var(--transition-alt);
    font-weight: 500;
    font-size: 0.9rem;
}

.back-to-home-alt a:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateX(-5px);
}

.back-to-home-alt i {
    margin-right: 0.5rem;
}

/* Auth page navigation */
.auth-nav-alt {
    position: absolute;
    top: 2rem;
    right: 2rem;
    z-index: 10;
    display: flex;
    gap: 1rem;
}

.auth-nav-alt a {
    display: flex;
    align-items: center;
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    background-color: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: var(--transition-alt);
    font-weight: 500;
    font-size: 0.9rem;
}

.auth-nav-alt a:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

.auth-nav-alt i {
    margin-right: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
    .auth-card-alt {
        margin: 0 1rem;
    }
    
    .auth-header-alt,
    .auth-body-alt,
    .auth-footer-alt {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
    
    .back-to-home-alt {
        top: 1rem;
        left: 1rem;
    }
}

/* Password visibility toggle */
.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted-alt);
    cursor: pointer;
    z-index: 2;
}

/* Role Selection */
.role-selection {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.role-option {
    flex: 1;
    position: relative;
}

.role-option input[type="radio"] {
    position: absolute;
    opacity: 0;
}

.role-option label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem 1rem;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius-alt);
    cursor: pointer;
    transition: var(--transition-alt);
    text-align: center;
}

.role-option input[type="radio"]:checked + label {
    background-color: rgba(79, 70, 229, 0.1);
    border-color: var(--primary-alt);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.role-option i {
    font-size: 2rem;
    margin-bottom: 0.75rem;
    color: var(--primary-alt);
}

.role-option .role-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.role-option .role-desc {
    font-size: 0.8rem;
    color: var(--text-muted-alt);
}

/* Animation */
.auth-card-alt {
    animation-name: slideUpFade;
    animation-duration: 0.6s;
    animation-timing-function: ease-out;
    animation-fill-mode: both;
}

@keyframes slideUpFade {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}
