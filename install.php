<?php
// Define installation mode
define('INSTALLING', true);

// Check if already installed
if (file_exists('config/installed.php')) {
    die("TimeTracker is already installed. If you want to reinstall, please delete the 'config/installed.php' file.");
}

// Include session configuration before including other configs
require_once 'config/session.php';

// Include configuration
require_once 'config/config.php';

// Function to create database tables
function createTables($pdo) {
    try {
        // Companies table
        $pdo->exec("CREATE TABLE IF NOT EXISTS companies (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            address TEXT,
            phone VARCHAR(20),
            email VARCHAR(100),
            logo VARCHAR(255),
            status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

        // Users table
        $pdo->exec("CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL,
            email VARCHAR(100) NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            company_id INT,
            role ENUM('superadmin', 'admin', 'staff', 'contractor') NOT NULL DEFAULT 'staff',
            status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL,
            UNIQUE KEY unique_username_company (username, company_id),
            UNIQUE KEY unique_email_company (email, company_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

        // Projects table
        $pdo->exec("CREATE TABLE IF NOT EXISTS projects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            company_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            status ENUM('active', 'completed', 'on-hold') NOT NULL DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

        // Timesheets table
        $pdo->exec("CREATE TABLE IF NOT EXISTS timesheets (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            project_id INT NOT NULL,
            company_id INT NOT NULL,
            date DATE NOT NULL,
            hours DECIMAL(5,2) NOT NULL,
            description TEXT,
            status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
            FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

        // Project assignments table
        $pdo->exec("CREATE TABLE IF NOT EXISTS project_assignments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            project_id INT NOT NULL,
            company_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
            FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
            UNIQUE KEY unique_assignment (user_id, project_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

        // Company settings table
        $pdo->exec("CREATE TABLE IF NOT EXISTS company_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            company_id INT NOT NULL,
            setting_key VARCHAR(50) NOT NULL,
            setting_value TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
            UNIQUE KEY unique_company_setting (company_id, setting_key)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

        // Global settings table
        $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
            setting_key VARCHAR(50) PRIMARY KEY,
            setting_value TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

        // Subscription plans table
        $pdo->exec("CREATE TABLE IF NOT EXISTS subscription_plans (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL,
            description TEXT,
            price DECIMAL(10,2) NOT NULL,
            max_users INT NOT NULL DEFAULT 0,
            max_projects INT NOT NULL DEFAULT 0,
            features TEXT,
            status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

        // Company subscriptions table
        $pdo->exec("CREATE TABLE IF NOT EXISTS company_subscriptions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            company_id INT NOT NULL,
            plan_id INT NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            status ENUM('active', 'expired', 'cancelled') NOT NULL DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
            FOREIGN KEY (plan_id) REFERENCES subscription_plans(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
        
        // Newsletter subscribers table
        $pdo->exec("CREATE TABLE IF NOT EXISTS newsletter_subscribers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(100) NOT NULL,
            subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('active', 'unsubscribed') NOT NULL DEFAULT 'active',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_email (email)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

        // Insert default global settings
        $pdo->exec("INSERT INTO settings (setting_key, setting_value) VALUES
            ('app_name', 'TimeTracker Pro'),
            ('app_version', '2.0.0'),
            ('allow_company_registration', '1'),
            ('default_subscription_plan', '1'),
            ('support_email', '<EMAIL>'),
            ('terms_url', 'https://timetrackerapp.com/terms'),
            ('privacy_url', 'https://timetrackerapp.com/privacy')
        ");

        // Insert default subscription plan
        $pdo->exec("INSERT INTO subscription_plans (name, description, price, max_users, max_projects, features, status) VALUES
            ('Free', 'Basic plan for small teams', 0.00, 5, 10, 'Basic time tracking,Daily/weekly views,Basic reporting', 'active'),
            ('Professional', 'Advanced features for growing businesses', 29.99, 20, 50, 'All Free features,Advanced reporting,Time approval workflow,Export to CSV/PDF', 'active'),
            ('Enterprise', 'Complete solution for large organizations', 99.99, 100, 200, 'All Professional features,API access,Priority support,Custom branding', 'active')
        ");

        return true;
    } catch (PDOException $e) {
        return "Error creating tables: " . $e->getMessage();
    }
}

// Function to create superadmin user
function createSuperAdminUser($pdo, $username, $email, $password, $firstName, $lastName) {
    try {
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, first_name, last_name, role, company_id)
                              VALUES (?, ?, ?, ?, ?, 'superadmin', NULL)");
        $stmt->execute([$username, $email, $hashedPassword, $firstName, $lastName]);

        return true;
    } catch (PDOException $e) {
        return "Error creating superadmin user: " . $e->getMessage();
    }
}

// Function to create default company
function createDefaultCompany($pdo, $name, $email) {
    try {
        $stmt = $pdo->prepare("INSERT INTO companies (name, email, status)
                              VALUES (?, ?, 'active')");
        $stmt->execute([$name, $email]);

        $companyId = $pdo->lastInsertId();

        // Insert default company settings
        $stmt = $pdo->prepare("INSERT INTO company_settings (company_id, setting_key, setting_value) VALUES
            (?, 'timesheet_mode', 'daily'),
            (?, 'company_name', ?),
            (?, 'allow_staff_registration', '1'),
            (?, 'allow_contractor_registration', '1'),
            (?, 'default_working_hours', '8'),
            (?, 'weekend_days', '0,6')
        ");
        $stmt->execute([
            $companyId, $companyId, $name, $companyId, $companyId, $companyId, $companyId
        ]);

        // Create subscription for the company
        $stmt = $pdo->prepare("INSERT INTO company_subscriptions (company_id, plan_id, start_date, end_date, status)
                              VALUES (?, 1, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 1 YEAR), 'active')");
        $stmt->execute([$companyId]);

        return $companyId;
    } catch (PDOException $e) {
        return "Error creating default company: " . $e->getMessage();
    }
}

// Function to create company admin user
function createCompanyAdminUser($pdo, $companyId, $username, $email, $password, $firstName, $lastName) {
    try {
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        $stmt = $pdo->prepare("INSERT INTO users (username, email, password, first_name, last_name, role, company_id)
                              VALUES (?, ?, ?, ?, ?, 'admin', ?)");
        $stmt->execute([$username, $email, $hashedPassword, $firstName, $lastName, $companyId]);

        return true;
    } catch (PDOException $e) {
        return "Error creating company admin user: " . $e->getMessage();
    }
}

// Process installation form
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate form data
    if (empty($_POST['username']) || empty($_POST['email']) || empty($_POST['password']) ||
        empty($_POST['first_name']) || empty($_POST['last_name']) ||
        empty($_POST['company_name']) || empty($_POST['company_email'])) {
        $error = "All fields are required";
    } else {
        // Create tables
        $tablesResult = createTables($pdo);

        if ($tablesResult === true) {
            // Create superadmin user
            $superadminResult = createSuperAdminUser(
                $pdo,
                $_POST['username'],
                $_POST['email'],
                $_POST['password'],
                $_POST['first_name'],
                $_POST['last_name']
            );

            if ($superadminResult === true) {
                // Create default company
                $companyResult = createDefaultCompany(
                    $pdo,
                    $_POST['company_name'],
                    $_POST['company_email']
                );

                if (is_numeric($companyResult)) {
                    // Create company admin user
                    $adminResult = createCompanyAdminUser(
                        $pdo,
                        $companyResult,
                        'admin',
                        $_POST['company_email'],
                        'admin123', // Default password for company admin
                        'Company',
                        'Admin'
                    );

                    if ($adminResult === true) {
                        // Create installed file
                        file_put_contents('config/installed.php', '<?php define("INSTALLED", true); ?>');

                        $success = "Installation completed successfully! <a href='index.php'>Go to login</a>";
                    } else {
                        $error = $adminResult;
                    }
                } else {
                    $error = $companyResult;
                }
            } else {
                $error = $superadminResult;
            }
        } else {
            $error = $tablesResult;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install TimeTracker Pro</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-10 col-lg-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0"><i class="fas fa-clock me-2"></i> Install TimeTracker Pro</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                            <div class="alert alert-success"><?php echo $success; ?></div>
                        <?php else: ?>
                            <form method="post">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h4 class="mb-3">Create Super Admin Account</h4>

                                        <div class="mb-3">
                                            <label for="username" class="form-label">Username</label>
                                            <input type="text" class="form-control" id="username" name="username" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="email" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="email" name="email" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="password" class="form-label">Password</label>
                                            <input type="password" class="form-control" id="password" name="password" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="first_name" class="form-label">First Name</label>
                                            <input type="text" class="form-control" id="first_name" name="first_name" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="last_name" class="form-label">Last Name</label>
                                            <input type="text" class="form-control" id="last_name" name="last_name" required>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <h4 class="mb-3">Create Default Company</h4>

                                        <div class="mb-3">
                                            <label for="company_name" class="form-label">Company Name</label>
                                            <input type="text" class="form-control" id="company_name" name="company_name" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="company_email" class="form-label">Company Email</label>
                                            <input type="email" class="form-control" id="company_email" name="company_email" required>
                                        </div>

                                        <div class="alert alert-info">
                                            <small>A company admin account will be automatically created with:</small>
                                            <ul class="mb-0">
                                                <li><small>Username: admin</small></li>
                                                <li><small>Password: admin123</small></li>
                                                <li><small>Email: [Company Email]</small></li>
                                            </ul>
                                            <small>You can change these credentials after installation.</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 mt-4">
                                    <button type="submit" class="btn btn-primary">Install TimeTracker Pro</button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
