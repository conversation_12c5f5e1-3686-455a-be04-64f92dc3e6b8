/* Futuristic Landing Page Styles */

/* Base Styles */
:root {
    --primary-color: #4361ee;
    --primary-dark: #3a56d4;
    --primary-light: #4895ef;
    --secondary-color: #4cc9f0;
    --accent-color: #7209b7;
    --accent-warm: #ff9e00; /* New warm accent color (orange) */
    --accent-warm-dark: #e67700; /* Darker version for hover states */
    --dark-color: #1a1a2e;
    --light-color: #f8f9fa;
    --text-color: #333333;
    --text-light: #f1f1f1;
    --text-muted: #6c757d;
    --gradient-1: linear-gradient(135deg, #3a56d4, #4cc9f0);
    --gradient-2: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    --gradient-3: linear-gradient(135deg, #4361ee, #3a0ca3);
    --gradient-4: linear-gradient(to right, #4361ee, #3a0ca3, #7209b7);
    --gradient-warm: linear-gradient(135deg, var(--accent-warm), var(--accent-warm-dark));
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --transition: all 0.3s ease;
    --navbar-height: 70px;
}

/* Accessibility - Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
    :root {
        --transition: none;
    }

    .floating-element {
        animation: none !important;
    }

    .pulse-btn {
        animation: none !important;
    }

    .feature-card:hover,
    .testimonial-card:hover,
    .pricing-card:hover {
        transform: none !important;
    }

    .feature-card.animated,
    .testimonial-card.animated,
    .pricing-card.animated,
    .step-item.animated {
        transition: opacity 0.1s linear !important;
    }
}

/* Navbar Styles for Landing Page */
.navbar.bg-transparent {
    z-index: 1030;
    transition: all 0.4s ease;
    padding-top: 20px;
    padding-bottom: 20px;
    box-shadow: none;
    backdrop-filter: blur(0px);
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
}

.navbar.bg-transparent .navbar-brand {
    color: white;
    font-weight: 700;
    letter-spacing: -0.5px;
    position: relative;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.navbar.bg-transparent .navbar-brand::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 30%;
    height: 3px;
    background: linear-gradient(to right, #ffffff, transparent);
    transition: width 0.3s ease;
}

.navbar.bg-transparent .navbar-brand:hover::after {
    width: 100%;
}

.navbar.bg-transparent .nav-link {
    color: rgba(255, 255, 255, 0.9);
    transition: var(--transition);
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    position: relative;
    font-weight: 500;
}

.navbar.bg-transparent .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: white;
    transition: all 0.3s ease;
    transform: translateX(-50%);
    opacity: 0;
}

.navbar.bg-transparent .nav-link:hover::after,
.navbar.bg-transparent .nav-link:focus::after {
    width: 80%;
    opacity: 1;
}

.navbar.bg-transparent .nav-link:hover,
.navbar.bg-transparent .nav-link:focus {
    color: white;
}

.navbar.bg-transparent .btn {
    padding: 0.5rem 1.25rem;
    margin-left: 0.5rem;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.navbar.bg-transparent .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.navbar.bg-transparent.scrolled {
    background-color: rgba(58, 86, 212, 0.95) !important;
    padding-top: 12px;
    padding-bottom: 12px;
    box-shadow: var(--shadow-md);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

body.landing-page {
    font-family: 'Poppins', sans-serif;
    background-color: var(--light-color);
    color: var(--text-color);
    overflow-x: hidden;
    line-height: 1.7;
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', 'Poppins', sans-serif;
    letter-spacing: -0.02em;
}

p, li, .btn, input, textarea, select {
    font-family: 'Poppins', sans-serif;
}

.small-text, .pricing-features ul li, .testimonial-info p, .pricing-price .period {
    letter-spacing: 0.01em;
}

/* Typography */
.text-gradient {
    background: var(--gradient-1);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

/* Hero Section */
.hero-section {
    position: relative;
    background: var(--gradient-4);
    color: white;
    padding: 120px 0 180px;
    overflow: hidden;
    min-height: 85vh;
    display: flex;
    align-items: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(76, 201, 240, 0.4) 0%, transparent 30%),
        radial-gradient(circle at 80% 70%, rgba(114, 9, 183, 0.4) 0%, transparent 30%),
        radial-gradient(circle, rgba(255, 255, 255, 0.15) 1px, transparent 1px);
    background-size: 100% 100%, 100% 100%, 20px 20px;
    opacity: 0.8;
    animation: pulse-bg 8s ease-in-out infinite alternate;
}

@keyframes pulse-bg {
    0% {
        opacity: 0.6;
    }
    100% {
        opacity: 0.9;
    }
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    width: 100%;
}

.hero-text h1 {
    margin-bottom: 1rem;
    font-weight: 800;
    font-size: 3.5rem;
    background: linear-gradient(to right, #ffffff, #e0e0ff);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.5px;
}

.hero-text h2 {
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.95);
}

.hero-text p {
    margin-bottom: 2rem;
    font-size: 1.1rem;
    opacity: 0.9;
    line-height: 1.7;
    max-width: 90%;
    max-width: 650px; /* Optimal reading length */
}

.hero-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.hero-image {
    position: relative;
    z-index: 1;
}

.hero-image img {
    border-radius: 12px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: all 0.5s ease;
}

.hero-image:hover img {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.2);
}

.hero-shape {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    filter: drop-shadow(0 -10px 10px rgba(0, 0, 0, 0.05));
}

/* Floating Animation */
.floating-element {
    animation: float 6s ease-in-out infinite;
    position: relative;
    filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.2));
    transition: var(--transition);
}

.floating-element::after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 5%;
    width: 90%;
    height: 20px;
    background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 70%);
    opacity: 0.5;
    z-index: -1;
    animation: shadow 6s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-15px) rotate(1deg);
    }
    50% {
        transform: translateY(-20px) rotate(0deg);
    }
    75% {
        transform: translateY(-10px) rotate(-1deg);
    }
    100% {
        transform: translateY(0px) rotate(0deg);
    }
}

@keyframes shadow {
    0% {
        transform: scale(1);
        opacity: 0.5;
    }
    50% {
        transform: scale(0.85);
        opacity: 0.3;
    }
    100% {
        transform: scale(1);
        opacity: 0.5;
    }
}

/* Button Styles */
.pulse-btn {
    position: relative;
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.7);
    animation: pulse 2s infinite;
    background: var(--gradient-1);
    border: none;
    transition: all 0.3s ease;
    overflow: hidden;
}

.btn-outline-light {
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    z-index: 1;
}

.btn-outline-light::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    z-index: -1;
}

.btn-outline-light:hover::before {
    width: 100%;
}

.pulse-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: translateX(-100%);
    transition: all 0.6s ease;
}

.pulse-btn:hover::before {
    transform: translateX(100%);
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.7);
    }
    50% {
        transform: scale(1.03);
        box-shadow: 0 0 0 8px rgba(67, 97, 238, 0.2);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
    }
}

/* Section Styles */
.section-heading {
    margin-bottom: 3rem;
}

.section-heading h2 {
    font-weight: 700;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.section-heading h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--primary-color);
}

.section-heading.text-center h2::after {
    left: 50%;
    transform: translateX(-50%);
}

.section-heading p {
    font-size: 1.1rem;
    color: var(--text-muted);
    max-width: 700px; /* Optimal reading length */
    margin-left: auto;
    margin-right: auto;
    line-height: 1.7;
}

/* Features Section */
.features-section {
    padding: 100px 0;
    position: relative;
}

.features-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, var(--primary-light), transparent);
    opacity: 0.3;
}

.feature-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    height: 100%;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 1;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md);
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--gradient-1);
    z-index: -1;
}

.feature-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: var(--gradient-1);
    opacity: 0.05;
    border-radius: 50%;
    transform: translate(30%, 30%);
    z-index: -1;
    transition: var(--transition);
}

.feature-card:hover::after {
    transform: translate(20%, 20%) scale(1.2);
    opacity: 0.1;
}

.feature-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: var(--light-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 1.8rem;
    color: var(--primary-color);
    transition: var(--transition);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.feature-card:hover .feature-icon {
    background: var(--gradient-1);
    color: white;
    transform: scale(1.1) rotate(5deg);
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    font-weight: 600;
    color: var(--dark-color);
}

/* How It Works Section */
.how-it-works-section {
    padding: 100px 0;
    background-color: #f8f9fa;
    position: relative;
    z-index: 1;
}

.how-it-works-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(circle, rgba(67, 97, 238, 0.03) 1px, transparent 1px);
    background-size: 20px 20px;
    z-index: -1;
}

.steps-container {
    max-width: 500px;
    margin: 0 auto;
}

.step-item {
    display: flex;
    margin-bottom: 30px;
    position: relative;
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-1);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 600;
    margin-right: 20px;
    flex-shrink: 0;
}

.step-content {
    padding-top: 5px;
}

.step-content h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    font-weight: 600;
}

/* Testimonials Section */
.testimonials-section {
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.testimonials-section::before,
.testimonials-section::after {
    content: '';
    position: absolute;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: var(--gradient-1);
    opacity: 0.05;
    z-index: 0;
}

.testimonials-section::before {
    top: -150px;
    left: -150px;
}

.testimonials-section::after {
    bottom: -150px;
    right: -150px;
}

.testimonial-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    height: 100%;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md);
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: 20px;
    right: 30px;
    font-size: 5rem;
    font-family: Georgia, serif;
    color: var(--primary-color);
    opacity: 0.1;
    line-height: 1;
}

.testimonial-content {
    margin-bottom: 20px;
    font-style: italic;
    position: relative;
    z-index: 1;
}

.testimonial-content p {
    font-size: 1rem;
    line-height: 1.6;
}

.testimonial-author {
    display: flex;
    align-items: center;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding-top: 20px;
}

.testimonial-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    border: 3px solid white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.testimonial-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.testimonial-info h4 {
    font-size: 1.1rem;
    margin-bottom: 5px;
    font-weight: 600;
    color: var(--dark-color);
}

.testimonial-info p {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
}

/* Pricing Section */
.pricing-section {
    padding: 100px 0;
    position: relative;
    z-index: 1;
}

.pricing-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.03) 0%, rgba(76, 201, 240, 0.03) 100%);
    z-index: -1;
}

.pricing-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    height: 100%;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md);
}

.pricing-card::after {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 100px;
    height: 100px;
    background: var(--gradient-1);
    opacity: 0.05;
    border-radius: 50%;
    z-index: 0;
}

.pricing-popular {
    border: 2px solid var(--accent-warm);
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
    background: linear-gradient(to bottom, white, rgba(255, 158, 0, 0.05));
}

.pricing-popular:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-badge {
    position: absolute;
    top: 0;
    right: 30px;
    background: var(--accent-warm);
    color: white;
    padding: 5px 15px;
    font-size: 0.8rem;
    font-weight: 600;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    z-index: 1;
}

.pricing-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.pricing-header h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.pricing-price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.pricing-price .currency {
    font-size: 1.5rem;
    font-weight: 500;
    vertical-align: super;
}

.pricing-price .period {
    font-size: 1rem;
    font-weight: 400;
    color: #6c757d;
}

.pricing-features {
    margin-bottom: 30px;
}

.pricing-features ul li {
    margin-bottom: 10px;
    font-size: 0.95rem;
}

.pricing-footer {
    text-align: center;
}

/* CTA Section */
.cta-section {
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    background: var(--accent-warm);
    opacity: 0.05;
    border-radius: 50%;
    z-index: 0;
}

.cta-container {
    background: var(--gradient-2);
    border-radius: 20px;
    padding: 60px 30px;
    color: white;
    box-shadow: var(--shadow-lg);
}

.cta-container .btn-primary {
    background: var(--gradient-warm);
    border-color: var(--accent-warm);
}

/* Micro-interactions and Form Elements */
.form-control, .form-select, .btn {
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
    transform: translateY(-2px);
}

.form-control::placeholder {
    transition: all 0.3s ease;
}

.form-control:focus::placeholder {
    opacity: 0.7;
    transform: translateX(5px);
}

.btn {
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
}

.btn:active::after {
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0) translate(-50%, -50%);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20) translate(-50%, -50%);
        opacity: 0;
    }
}

/* Accessibility Improvements */
.skip-link {
    position: absolute;
    top: -40px;
    left: 0;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    z-index: 9999;
    transition: top 0.3s ease;
}

.skip-link:focus {
    top: 0;
    outline: none;
}

/* Focus Styles */
.feature-card:focus,
.testimonial-card:focus,
.pricing-card:focus,
a:focus,
button:focus {
    outline: 3px solid var(--primary-color);
    outline-offset: 3px;
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
}

.feature-card:focus-visible,
.testimonial-card:focus-visible,
.pricing-card:focus-visible,
a:focus-visible,
button:focus-visible {
    outline: 3px solid var(--primary-color);
    outline-offset: 3px;
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
}

/* Animation Classes */
.feature-card, .testimonial-card, .pricing-card, .step-item {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.feature-card.animated, .testimonial-card.animated, .pricing-card.animated, .step-item.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Add delay to stagger animations */
.feature-card:nth-child(2), .testimonial-card:nth-child(2), .pricing-card:nth-child(2) {
    transition-delay: 0.2s;
}

.feature-card:nth-child(3), .testimonial-card:nth-child(3), .pricing-card:nth-child(3) {
    transition-delay: 0.4s;
}

.feature-card:nth-child(4), .step-item:nth-child(2) {
    transition-delay: 0.2s;
}

.feature-card:nth-child(5), .step-item:nth-child(3) {
    transition-delay: 0.4s;
}

.feature-card:nth-child(6), .step-item:nth-child(4) {
    transition-delay: 0.6s;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #121212;
        --dark-color: #f1f1f1;
    }

    body.landing-page {
        background-color: #121212;
        color: #f1f1f1;
    }

    .feature-card,
    .testimonial-card,
    .pricing-card {
        background: #1e1e1e;
        border-color: rgba(255, 255, 255, 0.1);
    }

    .feature-card h3,
    .testimonial-info h4,
    .pricing-header h3 {
        color: #f1f1f1;
    }

    .feature-icon {
        background: #2a2a2a;
    }

    .how-it-works-section,
    .pricing-section {
        background-color: #181818;
    }

    .text-muted,
    .lead.text-muted,
    .pricing-price .period {
        color: #aaaaaa !important;
    }

    .testimonial-info p {
        color: #aaaaaa;
    }

    .hero-shape path {
        fill: #121212;
    }

    .navbar.bg-transparent.scrolled {
        background-color: rgba(26, 26, 46, 0.95) !important;
    }

    .cta-container {
        background: #1e1e1e;
        border-color: rgba(255, 255, 255, 0.1);
    }
}

/* Responsive Adjustments */
/* Tablet Specific Optimizations (768px - 991px) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .hero-text h1 {
        font-size: 2.8rem;
    }

    .hero-section {
        padding: 100px 0 140px;
    }

    .feature-card, .testimonial-card, .pricing-card {
        height: calc(100% - 20px);
        margin: 10px 0;
    }

    .feature-icon {
        width: 65px;
        height: 65px;
        font-size: 1.6rem;
    }

    .step-image {
        max-width: 80%;
        margin: 0 auto;
    }

    .pricing-popular {
        transform: scale(1.03);
    }

    .pricing-popular:hover {
        transform: scale(1.03) translateY(-10px);
    }

    .cta-container {
        padding: 50px 30px;
    }
}

/* General Mobile Adjustments */
@media (max-width: 991.98px) {
    .hero-section {
        padding: 100px 0 140px;
        min-height: auto;
    }

    .hero-text {
        text-align: center;
        margin-bottom: 40px;
    }

    .hero-text h1 {
        font-size: 3rem;
    }

    .hero-text p {
        max-width: 100%;
        margin-left: auto;
        margin-right: auto;
    }

    .hero-buttons {
        justify-content: center;
    }

    .navbar.bg-transparent {
        background-color: rgba(67, 97, 238, 0.98) !important;
        padding-top: 10px;
        padding-bottom: 10px;
        box-shadow: var(--shadow-sm);
        position: fixed;
    }

    .navbar-collapse {
        background-color: rgba(67, 97, 238, 0.98);
        border-radius: 0 0 10px 10px;
        padding: 10px;
        margin: 0 -12px;
    }

    body.landing-page {
        padding-top: 0 !important; /* Override any padding added by JS on mobile */
    }
}

@media (max-width: 767.98px) {
    .hero-section {
        padding: 80px 0 120px;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }

    .hero-buttons .btn {
        width: 100%;
        margin-bottom: 10px;
    }

    /* Enhance mobile card styles */
    .feature-card, .testimonial-card, .pricing-card {
        padding: 25px 20px;
        margin-bottom: 15px;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .pricing-popular {
        transform: scale(1);
    }

    .pricing-popular:hover {
        transform: translateY(-10px);
    }

    .cta-container {
        padding: 40px 20px;
    }

    /* Improve spacing for mobile */
    .testimonial-content p {
        font-size: 0.95rem;
    }

    .step-number {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
}

@media (max-width: 575.98px) {
    .hero-section {
        padding: 80px 0 100px; /* Adjusted padding for small phones */
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .hero-text h2 {
        font-size: 1.2rem;
    }

    /* Optimize section spacing on mobile */
    .features-section,
    .how-it-works-section,
    .testimonials-section,
    .pricing-section,
    .cta-section {
        padding: 50px 0;
    }

    .text-center.mb-5 {
        margin-bottom: 2rem !important;
    }

    .hero-image img {
        transform: none !important;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    }

    .floating-element {
        animation: float 4s ease-in-out infinite;
    }
}

/* Sticky Navbar Styles */
.navbar-transparent {
    background-color: transparent;
    transition: background-color 0.3s ease, padding 0.3s ease, box-shadow 0.3s ease;
    padding-top: 15px;
    padding-bottom: 15px;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 1030;
}

.navbar-scrolled {
    background-color: var(--primary-color) !important;
    box-shadow: var(--shadow-md);
    padding-top: 10px;
    padding-bottom: 10px;
}

/* Smaller padding on mobile */
@media (max-width: 768px) {
    .navbar-scrolled {
        padding-top: 8px;
        padding-bottom: 8px;
    }
}

.navbar {
    min-height: var(--navbar-height);
    transition: var(--transition);
}

/* Adjust hero section padding to account for fixed navbar */
.hero-section {
    padding-top: var(--navbar-height);
}

/* Responsive navbar adjustments */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background-color: var(--primary-color);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 0.5rem;
        box-shadow: var(--shadow-md);
        max-height: 85vh;
        overflow-y: auto;
        transform-origin: top;
        animation: navbarCollapseIn 0.3s ease forwards;
    }
    
    @keyframes navbarCollapseIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .navbar-transparent .navbar-collapse {
        background-color: var(--primary-dark);
    }
    
    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .navbar-nav .nav-item:last-child .nav-link {
        border-bottom: none;
    }
}
