// Landing Page Alternative JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Sticky Navbar scroll effect
    const navbar = document.getElementById('landingNavbarAlt');
    
    if (navbar) {
        // Apply scroll effect immediately if page is already scrolled
        if (window.scrollY > 50) {
            navbar.classList.add('navbar-scrolled-alt');
        }

        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.classList.add('navbar-scrolled-alt');
            } else {
                navbar.classList.remove('navbar-scrolled-alt');
            }
        });

        // Handle navbar link clicks for smooth scrolling
        const navLinks = navbar.querySelectorAll('a.nav-link[href^="#"]');
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                // Only prevent default for same-page links
                if (this.getAttribute('href').startsWith('#')) {
                    e.preventDefault();
                    
                    // Close mobile menu when a link is clicked
                    const navbarCollapse = document.getElementById('navbarNavAlt');
                    if (navbarCollapse && navbarCollapse.classList.contains('show')) {
                        navbar.querySelector('.navbar-toggler').click();
                    }
                    
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    
                    if (targetElement) {
                        const navbarHeight = navbar.offsetHeight;
                        const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
                        const offsetPosition = targetPosition - navbarHeight;

                        window.scrollTo({
                            top: offsetPosition,
                            behavior: 'smooth'
                        });
                    }
                }
            });
        });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]:not(.nav-link)').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            if (this.getAttribute('href') === '#') return;
            
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            const navbarHeight = navbar ? navbar.offsetHeight : 0;

            if (targetElement) {
                // Adjust scroll position to account for sticky navbar
                window.scrollTo({
                    top: targetElement.offsetTop - navbarHeight - 20, // Extra 20px for spacing
                    behavior: 'smooth'
                });

                // Update URL hash without jumping
                history.pushState(null, null, targetId);
            }
        });
    });

    // Animated elements with Intersection Observer
    const animateElements = function() {
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        
        if (prefersReducedMotion) {
            // Respect user's preference for reduced motion
            document.querySelectorAll('.feature-card-alt, .step-item-alt, .testimonial-card-alt, .pricing-card-alt')
                .forEach(el => el.classList.add('animated-alt'));
            return;
        }
        
        if ('IntersectionObserver' in window) {
            const animationObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        
                        // Add a slight delay based on the element's position
                        const siblings = Array.from(element.parentNode.children);
                        const index = siblings.indexOf(element);
                        const delay = Math.min(index * 100, 300);
                        
                        setTimeout(() => {
                            element.classList.add('animated-alt');
                        }, delay);
                        
                        observer.unobserve(element);
                    }
                });
            }, {
                root: null,
                rootMargin: '0px 0px -10% 0px',
                threshold: 0.1
            });
            
            document.querySelectorAll('.feature-card-alt, .step-item-alt, .testimonial-card-alt, .pricing-card-alt')
                .forEach(element => {
                    element.classList.add('animate-on-scroll');
                    animationObserver.observe(element);
                });
        } else {
            // Fallback for browsers without Intersection Observer
            document.querySelectorAll('.feature-card-alt, .step-item-alt, .testimonial-card-alt, .pricing-card-alt')
                .forEach(el => el.classList.add('animated-alt'));
        }
    };
    
    animateElements();

    // Newsletter form handling
    const newsletterForm = document.getElementById('newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = this.querySelector('input[name="email"]').value;
            const newsletterMessage = document.getElementById('newsletter-message');
            const submitButton = this.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;
            
            // Show loading state
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Subscribing...';
            
            // Simulate AJAX request (replace with actual AJAX)
            setTimeout(() => {
                // Reset button
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
                
                // Show success message
                newsletterMessage.classList.add('text-success');
                newsletterMessage.classList.remove('text-danger');
                newsletterMessage.textContent = 'Thank you for subscribing! We\'ll be in touch.';
                
                // Clear form
                newsletterForm.reset();
            }, 1500);
        });
    }
});
