<?php
// Check if user is superadmin
if (!isSuperAdmin()) {
    header('Location: ' . BASE_URL . '?page=dashboard');
    exit;
}

// Process subscription plan status change
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['toggle_plan_status'])) {
    $planId = $_POST['plan_id'] ?? 0;
    $newStatus = $_POST['new_status'] ?? '';

    if (empty($planId) || empty($newStatus) || !in_array($newStatus, ['active', 'inactive'])) {
        $error = "Invalid request";
    } else {
        try {
            // Update plan status
            $stmt = $pdo->prepare("UPDATE subscription_plans SET status = ? WHERE id = ?");
            $stmt->execute([$newStatus, $planId]);

            $success = "Subscription plan status updated successfully";
        } catch (PDOException $e) {
            $error = "Error updating subscription plan status: " . $e->getMessage();
        }
    }
}

// Process add subscription plan form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_plan'])) {
    $planName = $_POST['name'] ?? '';
    $planDescription = $_POST['description'] ?? '';
    $planPrice = $_POST['price'] ?? '';
    $maxUsers = $_POST['max_users'] ?? '';
    $maxProjects = $_POST['max_projects'] ?? '';
    $features = $_POST['features'] ?? '';

    // Validate form data
    if (empty($planName) || !is_numeric($planPrice) || !is_numeric($maxUsers) || !is_numeric($maxProjects)) {
        $error = "All fields are required and price, max users, and max projects must be numbers";
    } else {
        try {
            // Insert plan
            $stmt = $pdo->prepare("INSERT INTO subscription_plans (name, description, price, max_users, max_projects, features, status)
                                  VALUES (?, ?, ?, ?, ?, ?, 'active')");
            $stmt->execute([$planName, $planDescription, $planPrice, $maxUsers, $maxProjects, $features]);

            $success = "Subscription plan added successfully";
        } catch (PDOException $e) {
            $error = "Error adding subscription plan: " . $e->getMessage();
        }
    }
}

// Process edit subscription plan form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['edit_plan'])) {
    $planId = $_POST['plan_id'] ?? 0;
    $planName = $_POST['name'] ?? '';
    $planDescription = $_POST['description'] ?? '';
    $planPrice = $_POST['price'] ?? '';
    $maxUsers = $_POST['max_users'] ?? '';
    $maxProjects = $_POST['max_projects'] ?? '';
    $features = $_POST['features'] ?? '';

    // Validate form data
    if (empty($planId) || empty($planName) || !is_numeric($planPrice) || !is_numeric($maxUsers) || !is_numeric($maxProjects)) {
        $error = "All fields are required and price, max users, and max projects must be numbers";
    } else {
        try {
            // Update plan
            $stmt = $pdo->prepare("UPDATE subscription_plans SET
                                  name = ?,
                                  description = ?,
                                  price = ?,
                                  max_users = ?,
                                  max_projects = ?,
                                  features = ?
                                  WHERE id = ?");
            $stmt->execute([$planName, $planDescription, $planPrice, $maxUsers, $maxProjects, $features, $planId]);

            $success = "Subscription plan updated successfully";
        } catch (PDOException $e) {
            $error = "Error updating subscription plan: " . $e->getMessage();
        }
    }
}

// Get all subscription plans
$stmt = $pdo->query("SELECT * FROM subscription_plans ORDER BY price");
$plans = $stmt->fetchAll();

// Get subscription stats
$stmt = $pdo->query("
    SELECT p.id, COUNT(cs.id) as subscription_count
    FROM subscription_plans p
    LEFT JOIN company_subscriptions cs ON p.id = cs.plan_id AND cs.status = 'active'
    GROUP BY p.id
    ORDER BY p.price
");
$planStats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

// Get recent subscriptions
$stmt = $pdo->query("
    SELECT cs.*, c.name as company_name, p.name as plan_name
    FROM company_subscriptions cs
    JOIN companies c ON cs.company_id = c.id
    JOIN subscription_plans p ON cs.plan_id = p.id
    ORDER BY cs.created_at DESC
    LIMIT 10
");
$recentSubscriptions = $stmt->fetchAll();
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3">Manage Subscriptions</h1>
        <p class="text-muted">View and manage subscription plans and company subscriptions</p>
    </div>
    <div class="col-md-6 text-md-end">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPlanModal">
            <i class="fas fa-plus"></i> Add Subscription Plan
        </button>
    </div>
</div>

<?php if ($error): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if ($success): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Subscription Plans</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Price</th>
                                <th>Limits</th>
                                <th>Active Subscriptions</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($plans)): ?>
                                <tr>
                                    <td colspan="6" class="text-center">No subscription plans found</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($plans as $plan): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo $plan['name']; ?></strong>
                                            <?php if (!empty($plan['description'])): ?>
                                                <small class="d-block text-muted"><?php echo $plan['description']; ?></small>
                                            <?php endif; ?>

                                            <?php if (!empty($plan['features'])): ?>
                                                <small class="d-block mt-1">
                                                    <a href="#" class="text-primary view-features"
                                                       data-bs-toggle="modal"
                                                       data-bs-target="#viewFeaturesModal"
                                                       data-plan-name="<?php echo htmlspecialchars($plan['name']); ?>"
                                                       data-features="<?php echo htmlspecialchars($plan['features']); ?>">
                                                        <i class="fas fa-list-ul"></i> View Features
                                                    </a>
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td>$<?php echo number_format($plan['price'], 2); ?></td>
                                        <td>
                                            <small class="d-block">Users: <?php echo $plan['max_users'] === '0' ? 'Unlimited' : $plan['max_users']; ?></small>
                                            <small class="d-block">Projects: <?php echo $plan['max_projects'] === '0' ? 'Unlimited' : $plan['max_projects']; ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $planStats[$plan['id']] ?? 0; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($plan['status'] === 'active'): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary edit-plan-btn"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#editPlanModal"
                                                        data-id="<?php echo $plan['id']; ?>"
                                                        data-name="<?php echo htmlspecialchars($plan['name']); ?>"
                                                        data-description="<?php echo htmlspecialchars($plan['description']); ?>"
                                                        data-price="<?php echo $plan['price']; ?>"
                                                        data-max-users="<?php echo $plan['max_users']; ?>"
                                                        data-max-projects="<?php echo $plan['max_projects']; ?>"
                                                        data-features="<?php echo htmlspecialchars($plan['features']); ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>

                                                <form method="post" class="d-inline" onsubmit="return confirm('Are you sure you want to <?php echo $plan['status'] === 'active' ? 'deactivate' : 'activate'; ?> this plan?');">
                                                    <input type="hidden" name="plan_id" value="<?php echo $plan['id']; ?>">
                                                    <input type="hidden" name="new_status" value="<?php echo $plan['status'] === 'active' ? 'inactive' : 'active'; ?>">
                                                    <input type="hidden" name="toggle_plan_status" value="1">

                                                    <button type="submit" class="btn btn-sm <?php echo $plan['status'] === 'active' ? 'btn-outline-danger' : 'btn-outline-success'; ?>">
                                                        <?php if ($plan['status'] === 'active'): ?>
                                                            <i class="fas fa-ban"></i>
                                                        <?php else: ?>
                                                            <i class="fas fa-check"></i>
                                                        <?php endif; ?>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Recent Subscriptions</h5>
            </div>
            <div class="card-body">
                <?php if (empty($recentSubscriptions)): ?>
                    <p class="text-muted">No recent subscriptions found.</p>
                <?php else: ?>
                    <div class="list-group">
                        <?php foreach ($recentSubscriptions as $subscription): ?>
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">
                                        <a href="<?php echo BASE_URL; ?>?page=superadmin-edit-company&id=<?php echo $subscription['company_id']; ?>">
                                            <?php echo $subscription['company_name']; ?>
                                        </a>
                                    </h6>
                                    <small>
                                        <?php if ($subscription['status'] === 'active'): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php elseif ($subscription['status'] === 'expired'): ?>
                                            <span class="badge bg-danger">Expired</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning text-dark">Cancelled</span>
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <p class="mb-1">
                                    <span class="badge bg-info"><?php echo $subscription['plan_name']; ?></span>
                                </p>
                                <small class="text-muted">
                                    <?php echo formatDate($subscription['start_date'], 'M d, Y'); ?> -
                                    <?php echo formatDate($subscription['end_date'], 'M d, Y'); ?>
                                </small>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add Plan Modal -->
<div class="modal fade" id="addPlanModal" tabindex="-1" aria-labelledby="addPlanModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post">
                <div class="modal-header">
                    <h5 class="modal-title" id="addPlanModalLabel">Add New Subscription Plan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Plan Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <input type="text" class="form-control" id="description" name="description">
                    </div>

                    <div class="mb-3">
                        <label for="price" class="form-label">Price (USD)</label>
                        <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                    </div>

                    <div class="mb-3">
                        <label for="max_users" class="form-label">Maximum Users</label>
                        <input type="number" class="form-control" id="max_users" name="max_users" min="1" required>
                    </div>

                    <div class="mb-3">
                        <label for="max_projects" class="form-label">Maximum Projects</label>
                        <input type="number" class="form-control" id="max_projects" name="max_projects" min="1" required>
                    </div>

                    <div class="mb-3">
                        <label for="features" class="form-label">Features (comma-separated)</label>
                        <textarea class="form-control" id="features" name="features" rows="5" placeholder="Basic time tracking,Daily/weekly views,Basic reporting"></textarea>
                        <div class="form-text">
                            Enter features separated by commas. Use "All X features" to include all features from another plan.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Common Features</label>
                        <div class="d-flex flex-wrap gap-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary feature-tag" data-feature="Basic time tracking">Basic time tracking</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary feature-tag" data-feature="Daily/weekly views">Daily/weekly views</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary feature-tag" data-feature="Basic reporting">Basic reporting</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary feature-tag" data-feature="Advanced reporting">Advanced reporting</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary feature-tag" data-feature="Time approval workflow">Time approval workflow</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary feature-tag" data-feature="Export to CSV/PDF">Export to CSV/PDF</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary feature-tag" data-feature="API access">API access</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary feature-tag" data-feature="Priority support">Priority support</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary feature-tag" data-feature="Custom branding">Custom branding</button>
                        </div>
                    </div>

                    <input type="hidden" name="add_plan" value="1">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Plan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Plan Modal -->
<div class="modal fade" id="editPlanModal" tabindex="-1" aria-labelledby="editPlanModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post">
                <div class="modal-header">
                    <h5 class="modal-title" id="editPlanModalLabel">Edit Subscription Plan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Plan Name</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <input type="text" class="form-control" id="edit_description" name="description">
                    </div>

                    <div class="mb-3">
                        <label for="edit_price" class="form-label">Price (USD)</label>
                        <input type="number" class="form-control" id="edit_price" name="price" step="0.01" min="0" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_max_users" class="form-label">Maximum Users</label>
                        <input type="number" class="form-control" id="edit_max_users" name="max_users" min="1" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_max_projects" class="form-label">Maximum Projects</label>
                        <input type="number" class="form-control" id="edit_max_projects" name="max_projects" min="1" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_features" class="form-label">Features (comma-separated)</label>
                        <textarea class="form-control" id="edit_features" name="features" rows="5"></textarea>
                        <div class="form-text">
                            Enter features separated by commas. Use "All X features" to include all features from another plan.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Common Features</label>
                        <div class="d-flex flex-wrap gap-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary edit-feature-tag" data-feature="Basic time tracking">Basic time tracking</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary edit-feature-tag" data-feature="Daily/weekly views">Daily/weekly views</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary edit-feature-tag" data-feature="Basic reporting">Basic reporting</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary edit-feature-tag" data-feature="Advanced reporting">Advanced reporting</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary edit-feature-tag" data-feature="Time approval workflow">Time approval workflow</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary edit-feature-tag" data-feature="Export to CSV/PDF">Export to CSV/PDF</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary edit-feature-tag" data-feature="API access">API access</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary edit-feature-tag" data-feature="Priority support">Priority support</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary edit-feature-tag" data-feature="Custom branding">Custom branding</button>
                        </div>
                    </div>

                    <input type="hidden" id="edit_plan_id" name="plan_id" value="">
                    <input type="hidden" name="edit_plan" value="1">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Plan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Features Modal -->
<div class="modal fade" id="viewFeaturesModal" tabindex="-1" aria-labelledby="viewFeaturesModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewFeaturesModalLabel">Plan Features</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6 id="feature-plan-name" class="mb-3"></h6>
                <ul id="feature-list" class="list-group">
                    <!-- Features will be populated here -->
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle edit plan button clicks
    const editButtons = document.querySelectorAll('.edit-plan-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const description = this.getAttribute('data-description');
            const price = this.getAttribute('data-price');
            const maxUsers = this.getAttribute('data-max-users');
            const maxProjects = this.getAttribute('data-max-projects');
            const features = this.getAttribute('data-features');

            document.getElementById('edit_plan_id').value = id;
            document.getElementById('edit_name').value = name;
            document.getElementById('edit_description').value = description;
            document.getElementById('edit_price').value = price;
            document.getElementById('edit_max_users').value = maxUsers;
            document.getElementById('edit_max_projects').value = maxProjects;
            document.getElementById('edit_features').value = features;
        });
    });

    // Handle view features button clicks
    const viewFeatureButtons = document.querySelectorAll('.view-features');
    viewFeatureButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const planName = this.getAttribute('data-plan-name');
            const features = this.getAttribute('data-features');

            // Set plan name
            document.getElementById('feature-plan-name').textContent = planName + ' Plan Features';

            // Clear existing features
            const featureList = document.getElementById('feature-list');
            featureList.innerHTML = '';

            // Add features to list
            if (features) {
                const featureArray = features.split(',');
                featureArray.forEach(feature => {
                    const featureItem = document.createElement('li');
                    featureItem.className = 'list-group-item';

                    // Check if it's a reference to another plan's features
                    if (feature.trim().startsWith('All') && feature.trim().endsWith('features')) {
                        featureItem.innerHTML = `<strong>${feature.trim()}</strong>`;
                    } else {
                        featureItem.textContent = feature.trim();
                    }

                    featureList.appendChild(featureItem);
                });
            } else {
                const noFeatures = document.createElement('li');
                noFeatures.className = 'list-group-item text-muted';
                noFeatures.textContent = 'No features defined for this plan';
                featureList.appendChild(noFeatures);
            }
        });
    });

    // Handle feature tag clicks for add plan form
    const featureTags = document.querySelectorAll('.feature-tag');
    featureTags.forEach(tag => {
        tag.addEventListener('click', function() {
            const feature = this.getAttribute('data-feature');
            const featuresTextarea = document.getElementById('features');

            // Add feature to textarea if not already present
            const currentFeatures = featuresTextarea.value.split(',').map(f => f.trim());
            if (!currentFeatures.includes(feature)) {
                if (featuresTextarea.value && featuresTextarea.value.trim() !== '') {
                    featuresTextarea.value += ', ' + feature;
                } else {
                    featuresTextarea.value = feature;
                }
            }
        });
    });

    // Handle feature tag clicks for edit plan form
    const editFeatureTags = document.querySelectorAll('.edit-feature-tag');
    editFeatureTags.forEach(tag => {
        tag.addEventListener('click', function() {
            const feature = this.getAttribute('data-feature');
            const featuresTextarea = document.getElementById('edit_features');

            // Add feature to textarea if not already present
            const currentFeatures = featuresTextarea.value.split(',').map(f => f.trim());
            if (!currentFeatures.includes(feature)) {
                if (featuresTextarea.value && featuresTextarea.value.trim() !== '') {
                    featuresTextarea.value += ', ' + feature;
                } else {
                    featuresTextarea.value = feature;
                }
            }
        });
    });
});
</script>
