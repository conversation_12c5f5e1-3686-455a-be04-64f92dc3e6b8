<?php
// Process reset password form
$error = '';
$success = '';
$validToken = false;
$token = $_GET['token'] ?? '';

if (empty($token)) {
    $error = "Invalid or missing token";
} else {
    // Validate token
    $tokenData = validatePasswordResetToken($token);
    if ($tokenData) {
        $validToken = true;
        $email = $tokenData['email'];
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $password = $_POST['password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';
            
            if (empty($password) || empty($confirm_password)) {
                $error = "All fields are required";
            } elseif ($password !== $confirm_password) {
                $error = "Passwords do not match";
            } elseif (strlen($password) < 8) {
                $error = "Password must be at least 8 characters long";
            } else {
                // Update password
                if (resetUserPassword($email, $password)) {
                    // Delete used token
                    deletePasswordResetToken($token);
                    $success = "Your password has been reset successfully.";
                } else {
                    $error = "An error occurred. Please try again.";
                }
            }
        }
    } else {
        $error = "Invalid or expired token. Please request a new password reset link.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - TimeTracker</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/fontawesome.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/auth-alternative.css">
    
    <!-- Favicon -->
    <link rel="icon" href="<?php echo BASE_URL; ?>assets/img/favicon.png">
</head>
<body>    <div class="auth-page-alt">
        <div class="back-to-home-alt">
            <a href="<?php echo BASE_URL; ?>landing-alternative.php">
                <i class="fas fa-arrow-left"></i> Back to Home
            </a>
        </div>
        
        <div class="auth-nav-alt">
            <a href="<?php echo BASE_URL; ?>?page=login-alternative">
                <i class="fas fa-sign-in-alt"></i> Login
            </a>
            <?php if (getSetting('allow_staff_registration', '1') == '1' || getSetting('allow_contractor_registration', '1') == '1'): ?>
            <a href="<?php echo BASE_URL; ?>?page=register-alternative">
                <i class="fas fa-user-plus"></i> Register
            </a>
            <?php endif; ?>
        </div>
        
        <div class="container">
            <div class="auth-card-alt">
                <div class="auth-header-alt">
                    <div class="brand text-center mb-4">
                        <img src="<?php echo BASE_URL; ?>assets/img/logo.png" alt="TimeTracker Logo" height="40">
                    </div>
                    <h3>Reset Your Password</h3>
                    <p>Create a new password for your account</p>
                </div>
                
                <div class="auth-body-alt">
                    <?php if ($error): ?>
                        <div class="alert-alt alert-danger-alt">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo $error; ?>
                        </div>
                        
                        <?php if (!$validToken): ?>
                            <div class="text-center mt-4">
                                <a href="<?php echo BASE_URL; ?>?page=forgot-password-alternative" class="btn-alt btn-primary-alt">
                                    <i class="fas fa-redo me-2"></i> Request New Reset Link
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert-alt alert-success-alt">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo $success; ?>
                        </div>
                        <div class="text-center mt-4">
                            <a href="<?php echo BASE_URL; ?>?page=login-alternative" class="btn-alt btn-primary-alt">
                                <i class="fas fa-sign-in-alt me-2"></i> Go to Login
                            </a>
                        </div>
                    <?php elseif ($validToken): ?>
                        <form method="post">
                            <div class="form-group-alt">
                                <label for="password">New Password</label>
                                <div class="input-group-alt">
                                    <input type="password" class="form-control-alt" id="password" name="password" placeholder="Enter your new password" required>
                                    <span class="input-group-icon">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <span class="password-toggle" id="togglePassword">
                                        <i class="far fa-eye"></i>
                                    </span>
                                </div>
                                <small class="text-muted">Password must be at least 8 characters long</small>
                            </div>
                            
                            <div class="form-group-alt">
                                <label for="confirm_password">Confirm New Password</label>
                                <div class="input-group-alt">
                                    <input type="password" class="form-control-alt" id="confirm_password" name="confirm_password" placeholder="Confirm your new password" required>
                                    <span class="input-group-icon">
                                        <i class="fas fa-lock-open"></i>
                                    </span>
                                    <span class="password-toggle" id="toggleConfirmPassword">
                                        <i class="far fa-eye"></i>
                                    </span>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn-alt btn-primary-alt btn-block-alt">
                                <i class="fas fa-key me-2"></i> Reset Password
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
                  <div class="auth-footer-alt">
                    <p>
                        Remember your password? 
                        <a href="<?php echo BASE_URL; ?>?page=login-alternative" class="auth-link fw-bold">
                            Sign In
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>    <!-- JavaScript Files -->
    <script src="<?php echo BASE_URL; ?>assets/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/theme-switcher.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/navigation-helper.js"></script>
    <script>
        // Password visibility toggle
        document.addEventListener('DOMContentLoaded', function() {
            const togglePassword = document.getElementById('togglePassword');
            const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            
            if (togglePassword && passwordInput) {
                togglePassword.addEventListener('click', function() {
                    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordInput.setAttribute('type', type);
                    
                    // Toggle eye icon
                    this.querySelector('i').classList.toggle('fa-eye');
                    this.querySelector('i').classList.toggle('fa-eye-slash');
                });
            }
            
            if (toggleConfirmPassword && confirmPasswordInput) {
                toggleConfirmPassword.addEventListener('click', function() {
                    const type = confirmPasswordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    confirmPasswordInput.setAttribute('type', type);
                    
                    // Toggle eye icon
                    this.querySelector('i').classList.toggle('fa-eye');
                    this.querySelector('i').classList.toggle('fa-eye-slash');
                });
            }
            
            // Password validation
            if (passwordInput && confirmPasswordInput) {
                confirmPasswordInput.addEventListener('input', function() {
                    if (passwordInput.value !== confirmPasswordInput.value) {
                        confirmPasswordInput.setCustomValidity("Passwords do not match");
                    } else {
                        confirmPasswordInput.setCustomValidity("");
                    }
                });
                
                passwordInput.addEventListener('input', function() {
                    if (confirmPasswordInput.value && passwordInput.value !== confirmPasswordInput.value) {
                        confirmPasswordInput.setCustomValidity("Passwords do not match");
                    } else {
                        confirmPasswordInput.setCustomValidity("");
                    }
                });
            }
        });
    </script>
</body>
</html>
