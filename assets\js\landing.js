// Landing Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Sticky Navbar scroll effect
    const navbar = document.getElementById('landingNavbar');
    const heroSection = document.querySelector('.hero-section');
    
    if (navbar && !navbar.classList.contains('bg-primary')) {
        // Apply scroll effect immediately if page is already scrolled
        if (window.scrollY > 50) {
            navbar.classList.add('navbar-scrolled');
        }

        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.classList.add('navbar-scrolled');
            } else {
                navbar.classList.remove('navbar-scrolled');
            }
        });

        // Handle navbar link clicks for smooth scrolling
        const navLinks = navbar.querySelectorAll('a.nav-link[href^="#"]');
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                // Only prevent default for same-page links
                if (this.getAttribute('href').startsWith('#')) {
                    e.preventDefault();
                    
                    // Close mobile menu when a link is clicked
                    const navbarCollapse = document.getElementById('navbarNav');
                    if (navbarCollapse && navbarCollapse.classList.contains('show')) {
                        navbar.querySelector('.navbar-toggler').click();
                    }
                    
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    
                    if (targetElement) {
                        const navbarHeight = navbar.offsetHeight;
                        const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
                        const offsetPosition = targetPosition - navbarHeight;

                        window.scrollTo({
                            top: offsetPosition,
                            behavior: 'smooth'
                        });
                    }
                }
            });
        });

        // Handle window resize to update hero height
        window.addEventListener('resize', function() {
            if (heroSection) {
                heroHeight = heroSection.offsetHeight;
            }
        });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');

            if (targetId === '#') return;

            const targetElement = document.querySelector(targetId);
            const navbarHeight = navbar ? navbar.offsetHeight : 0;

            if (targetElement) {
                // Adjust scroll position to account for sticky navbar
                window.scrollTo({
                    top: targetElement.offsetTop - navbarHeight - 20, // Extra 20px for spacing
                    behavior: 'smooth'
                });

                // Update URL hash without jumping
                history.pushState(null, null, targetId);
            }
        });
    });

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    if (!prefersReducedMotion) {
        // Use Intersection Observer for better performance
        if ('IntersectionObserver' in window) {
            const animationObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;

                        // Add a slight delay based on the element's position in its container
                        const siblings = Array.from(element.parentNode.children);
                        const index = siblings.indexOf(element);
                        const delay = Math.min(index * 100, 300); // Cap delay at 300ms

                        setTimeout(() => {
                            element.classList.add('animated');
                        }, delay);

                        // Unobserve after animation to improve performance
                        observer.unobserve(element);
                    }
                });
            }, {
                root: null,
                rootMargin: '0px 0px -50px 0px',
                threshold: 0.15
            });

            // Observe all animatable elements
            document.querySelectorAll('.feature-card, .testimonial-card, .pricing-card, .step-item').forEach(element => {
                animationObserver.observe(element);
            });
        } else {
            // Fallback for browsers without Intersection Observer
            let scrollTimeout;
            const animateOnScroll = function() {
                const elements = document.querySelectorAll('.feature-card:not(.animated), .testimonial-card:not(.animated), .pricing-card:not(.animated), .step-item:not(.animated)');

                if (elements.length === 0) {
                    // Remove scroll listener when all elements are animated
                    window.removeEventListener('scroll', scrollListener);
                    return;
                }

                elements.forEach(element => {
                    const elementPosition = element.getBoundingClientRect().top;
                    const windowHeight = window.innerHeight;

                    if (elementPosition < windowHeight - 100) {
                        element.classList.add('animated');
                    }
                });
            };

            // Debounced scroll handler
            const scrollListener = function() {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(animateOnScroll, 10);
            };

            // Run once on page load
            animateOnScroll();

            // Run on scroll with debouncing
            window.addEventListener('scroll', scrollListener);
        }
    } else {
        // For reduced motion preference, just show elements without animation
        const elements = document.querySelectorAll('.feature-card, .testimonial-card, .pricing-card, .step-item');
        elements.forEach(element => element.classList.add('animated'));
    }

    // Newsletter subscription form handling with AJAX
    const newsletterForm = document.getElementById('newsletter-form');
    const newsletterMessage = document.getElementById('newsletter-message');

    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = this.querySelector('input[name="email"]').value;
            const formData = new FormData();
            formData.append('email', email);
            formData.append('action', 'subscribe_newsletter');

            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.textContent;
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Subscribing...';

            // Send AJAX request
            fetch(this.getAttribute('action'), {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Reset button
                submitButton.disabled = false;
                submitButton.textContent = originalButtonText;

                // Show result message
                if (data.success) {
                    newsletterMessage.classList.add('text-success');
                    newsletterMessage.classList.remove('text-danger');
                    newsletterMessage.textContent = data.message;

                    // Clear form
                    this.reset();

                    // Track successful subscription if analytics is available
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'newsletter_subscription', {
                            'event_category': 'Engagement',
                            'event_label': 'Footer Form'
                        });
                    }
                } else {
                    newsletterMessage.classList.add('text-danger');
                    newsletterMessage.classList.remove('text-success');
                    newsletterMessage.textContent = data.message;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                submitButton.disabled = false;
                submitButton.textContent = originalButtonText;

                newsletterMessage.classList.add('text-danger');
                newsletterMessage.classList.remove('text-success');
                newsletterMessage.textContent = 'An error occurred. Please try again later.';
            });
        });
    }
});
