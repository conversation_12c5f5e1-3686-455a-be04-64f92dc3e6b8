/* Authentication Pages Styling - Matching Landing Page Design */

/* Base Styles - Matching landing.css variables */
:root {
    --primary-color: #4361ee;
    --primary-dark: #3a56d4;
    --primary-light: #4895ef;
    --secondary-color: #4cc9f0;
    --accent-color: #7209b7;
    --accent-warm: #ff9e00;
    --accent-warm-dark: #e67700;
    --dark-color: #1a1a2e;
    --light-color: #f8f9fa;
    --text-color: #333333;
    --text-light: #f1f1f1;
    --text-muted: #6c757d;
    --gradient-1: linear-gradient(135deg, #3a56d4, #4cc9f0);
    --gradient-2: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    --gradient-3: linear-gradient(135deg, #4361ee, #3a0ca3);
    --gradient-4: linear-gradient(to right, #4361ee, #3a0ca3, #7209b7);
    --gradient-warm: linear-gradient(135deg, var(--accent-warm), var(--accent-warm-dark));
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --border-radius: 0.5rem;
    --border-radius-lg: 1rem;
}

/* Auth Page Container */
.auth-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    background: var(--gradient-4);
    position: relative;
    overflow: hidden;
}

/* Background Pattern */
.auth-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(76, 201, 240, 0.4) 0%, transparent 30%),
        radial-gradient(circle at 80% 70%, rgba(114, 9, 183, 0.4) 0%, transparent 30%),
        radial-gradient(circle, rgba(255, 255, 255, 0.15) 1px, transparent 1px);
    background-size: 100% 100%, 100% 100%, 20px 20px;
    opacity: 0.8;
    animation: pulse-bg 8s ease-in-out infinite alternate;
}

@keyframes pulse-bg {
    0% {
        opacity: 0.6;
    }
    100% {
        opacity: 0.9;
    }
}

/* Auth Card */
.auth-card {
    position: relative;
    z-index: 10;
    width: 100%;
    max-width: 500px;
    background-color: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.auth-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.2);
}

/* Card Header */
.auth-card .card-header {
    background: var(--gradient-1);
    color: white;
    border-bottom: none;
    padding: 1.5rem;
    position: relative;
}

.auth-card .card-header h4 {
    margin: 0;
    font-weight: 700;
    font-size: 1.5rem;
    letter-spacing: -0.02em;
}

/* Card Body */
.auth-card .card-body {
    padding: 2rem;
}

/* Form Elements */
.auth-card .form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.auth-card .form-control {
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background-color: rgba(247, 250, 255, 0.8);
    transition: all 0.3s ease;
}

.auth-card .form-control:focus {
    border-color: var(--primary-light);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
    background-color: white;
}

/* Buttons */
.auth-card .btn-primary {
    background: var(--gradient-1);
    border: none;
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.auth-card .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.auth-card .btn-primary:active {
    transform: translateY(0);
}

/* Links */
.auth-card a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.auth-card a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Alert Messages */
.auth-card .alert {
    border-radius: 0.5rem;
    border: none;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.auth-card .alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.auth-card .alert-success {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

/* Back to Home Link */
.back-to-home {
    position: absolute;
    top: 1rem;
    left: 1rem;
    z-index: 20;
}

.back-to-home a {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    font-weight: 500;
    transition: all 0.3s ease;
}

.back-to-home a:hover {
    transform: translateX(-5px);
}

.back-to-home i {
    margin-right: 0.5rem;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
    .auth-card {
        max-width: 100%;
    }
    
    .auth-card .card-body {
        padding: 1.5rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .auth-card {
        background-color: #1e1e1e;
        color: #f1f1f1;
    }
    
    .auth-card .card-header {
        background: var(--gradient-3);
    }
    
    .auth-card .form-label {
        color: #f1f1f1;
    }
    
    .auth-card .form-control {
        background-color: rgba(30, 30, 30, 0.8);
        border-color: rgba(255, 255, 255, 0.1);
        color: #f1f1f1;
    }
    
    .auth-card .form-control:focus {
        background-color: #2a2a2a;
    }
    
    .auth-card a {
        color: var(--primary-light);
    }
    
    .auth-card a:hover {
        color: var(--secondary-color);
    }
    
    .auth-card .alert-danger {
        background-color: rgba(220, 53, 69, 0.2);
    }
    
    .auth-card .alert-success {
        background-color: rgba(25, 135, 84, 0.2);
    }
}
