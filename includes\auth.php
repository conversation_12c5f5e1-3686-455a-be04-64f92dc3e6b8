<?php
/**
 * Authentication functions for the TimeTracker application
 */

/**
 * Check if a user is logged in
 *
 * @return bool True if the user is logged in, false otherwise
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * Check if the logged-in user is a superadmin
 *
 * @return bool True if the user is a superadmin, false otherwise
 */
function isSuperAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'superadmin';
}

/**
 * Check if the logged-in user is an admin
 *
 * @return bool True if the user is an admin, false otherwise
 */
function isAdmin() {
    return isset($_SESSION['user_role']) && ($_SESSION['user_role'] === 'admin' || $_SESSION['user_role'] === 'superadmin');
}

/**
 * Check if the logged-in user is a staff member
 *
 * @return bool True if the user is a staff member, false otherwise
 */
function isStaff() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'staff';
}

/**
 * Check if the logged-in user is a contractor
 *
 * @return bool True if the user is a contractor, false otherwise
 */
function isContractor() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'contractor';
}

/**
 * Get the current user's company ID
 *
 * @return int|null The company ID or null if not available
 */
function getCurrentCompanyId() {
    return isset($_SESSION['company_id']) ? $_SESSION['company_id'] : null;
}

/**
 * Get company by ID
 *
 * @param int $companyId The company ID
 * @return array|false The company data or false if not found
 */
function getCompanyById($companyId) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM companies WHERE id = ?");
    $stmt->execute([$companyId]);

    return $stmt->fetch();
}

/**
 * Get the current user's ID
 *
 * @return int|null The user ID or null if not logged in
 */
function getCurrentUserId() {
    return isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
}

/**
 * Get the current user's data
 *
 * @return array|null The user data or null if not logged in
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }

    return getUserById($_SESSION['user_id']);
}

/**
 * Authenticate a user
 *
 * @param string $username The username
 * @param string $password The password
 * @return bool True if authentication was successful, false otherwise
 */
function authenticateUser($username, $password) {
    global $pdo;

    // First try to find a superadmin (they don't have company_id)
    $stmt = $pdo->prepare("SELECT * FROM users WHERE (username = ? OR email = ?) AND status = 'active' AND role = 'superadmin'");
    $stmt->execute([$username, $username]);

    $user = $stmt->fetch();

    if (!$user) {
        // If not a superadmin, look for regular users
        $stmt = $pdo->prepare("
            SELECT u.*, c.name as company_name, c.status as company_status
            FROM users u
            JOIN companies c ON u.company_id = c.id
            WHERE (u.username = ? OR u.email = ?) AND u.status = 'active'
        ");
        $stmt->execute([$username, $username]);

        $user = $stmt->fetch();

        // Check if company is active
        if ($user && $user['company_status'] !== 'active') {
            return false; // Company is inactive
        }
    }

    if ($user && password_verify($password, $user['password'])) {
        // Set session variables
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['username'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['user_full_name'] = $user['first_name'] . ' ' . $user['last_name'];

        // Set company context if applicable
        if (isset($user['company_id'])) {
            $_SESSION['company_id'] = $user['company_id'];
            $_SESSION['company_name'] = $user['company_name'];
        }

        return true;
    }

    return false;
}

/**
 * Register a new user
 *
 * @param array $userData The user data
 * @return int|string The user ID if registration was successful, error message otherwise
 */
function registerUser($userData) {
    global $pdo;

    try {
        $companyId = isset($userData['company_id']) ? $userData['company_id'] : getCurrentCompanyId();

        // Check if username or email already exists in the same company
        if ($companyId) {
            $stmt = $pdo->prepare("SELECT * FROM users WHERE (username = ? OR email = ?) AND company_id = ?");
            $stmt->execute([$userData['username'], $userData['email'], $companyId]);
        } else {
            // For superadmins (no company)
            $stmt = $pdo->prepare("SELECT * FROM users WHERE (username = ? OR email = ?) AND company_id IS NULL");
            $stmt->execute([$userData['username'], $userData['email']]);
        }

        if ($stmt->rowCount() > 0) {
            $existingUser = $stmt->fetch();

            if ($existingUser['username'] === $userData['username']) {
                return "Username already exists";
            } else {
                return "Email already exists";
            }
        }

        // Hash password
        $hashedPassword = password_hash($userData['password'], PASSWORD_DEFAULT);

        // Insert user
        if ($companyId) {
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password, first_name, last_name, role, company_id)
                                  VALUES (?, ?, ?, ?, ?, ?, ?)");

            $stmt->execute([
                $userData['username'],
                $userData['email'],
                $hashedPassword,
                $userData['first_name'],
                $userData['last_name'],
                $userData['role'],
                $companyId
            ]);
        } else {
            // For superadmins (no company)
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password, first_name, last_name, role, company_id)
                                  VALUES (?, ?, ?, ?, ?, ?, NULL)");

            $stmt->execute([
                $userData['username'],
                $userData['email'],
                $hashedPassword,
                $userData['first_name'],
                $userData['last_name'],
                $userData['role']
            ]);
        }

        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return "Registration failed: " . $e->getMessage();
    }
}

/**
 * Update a user's profile
 *
 * @param int $userId The user ID
 * @param array $userData The user data to update
 * @return bool|string True if update was successful, error message otherwise
 */
function updateUserProfile($userId, $userData) {
    global $pdo;

    try {
        // Check if email already exists for another user
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND id != ?");
        $stmt->execute([$userData['email'], $userId]);

        if ($stmt->rowCount() > 0) {
            return "Email already exists";
        }

        // Update user
        $stmt = $pdo->prepare("UPDATE users SET
                              email = ?,
                              first_name = ?,
                              last_name = ?
                              WHERE id = ?");

        $stmt->execute([
            $userData['email'],
            $userData['first_name'],
            $userData['last_name'],
            $userId
        ]);

        return true;
    } catch (PDOException $e) {
        return "Update failed: " . $e->getMessage();
    }
}

/**
 * Change a user's password
 *
 * @param int $userId The user ID
 * @param string $currentPassword The current password
 * @param string $newPassword The new password
 * @return bool|string True if change was successful, error message otherwise
 */
function changeUserPassword($userId, $currentPassword, $newPassword) {
    global $pdo;

    try {
        // Get user
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$userId]);

        $user = $stmt->fetch();

        if (!$user) {
            return "User not found";
        }

        // Verify current password
        if (!password_verify($currentPassword, $user['password'])) {
            return "Current password is incorrect";
        }

        // Hash new password
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

        // Update password
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
        $stmt->execute([$hashedPassword, $userId]);

        return true;
    } catch (PDOException $e) {
        return "Password change failed: " . $e->getMessage();
    }
}

/**
 * Log out the current user
 */
function logoutUser() {
    // Unset all session variables
    $_SESSION = [];

    // Destroy the session
    session_destroy();
}
?>
