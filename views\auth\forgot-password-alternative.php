<?php
// Process forgot password form
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';

    if (empty($email)) {
        $error = "Email address is required";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } else {
        // Check if email exists
        if (emailExists($email)) {
            // Generate token and send reset email
            $token = generatePasswordResetToken($email);
            $resetLink = BASE_URL . '?page=reset-password-alternative&token=' . $token;
            
            // For demonstration purposes, we'll just show the link
            // In a real application, you would send an email with the link
            $success = "Password reset instructions have been sent to your email address.";
            
            // Uncomment for debugging
            // $success .= "<br>Debug: <a href='$resetLink'>Reset Link</a>";
        } else {
            $error = "No account found with that email address";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - TimeTracker</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/fontawesome.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/auth-alternative.css">
    
    <!-- Favicon -->
    <link rel="icon" href="<?php echo BASE_URL; ?>assets/img/favicon.png">
</head>
<body>    <div class="auth-page-alt">
        <div class="back-to-home-alt">
            <a href="<?php echo BASE_URL; ?>landing-alternative.php">
                <i class="fas fa-arrow-left"></i> Back to Home
            </a>
        </div>
        
        <div class="auth-nav-alt">
            <a href="<?php echo BASE_URL; ?>?page=login-alternative">
                <i class="fas fa-sign-in-alt"></i> Login
            </a>
            <?php if (getSetting('allow_staff_registration', '1') == '1' || getSetting('allow_contractor_registration', '1') == '1'): ?>
            <a href="<?php echo BASE_URL; ?>?page=register-alternative">
                <i class="fas fa-user-plus"></i> Register
            </a>
            <?php endif; ?>
        </div>
        
        <div class="container">
            <div class="auth-card-alt">
                <div class="auth-header-alt">
                    <div class="brand text-center mb-4">
                        <img src="<?php echo BASE_URL; ?>assets/img/logo.png" alt="TimeTracker Logo" height="40">
                    </div>
                    <h3>Forgot Password</h3>
                    <p>Enter your email to receive password reset instructions</p>
                </div>
                
                <div class="auth-body-alt">
                    <?php if ($error): ?>
                        <div class="alert-alt alert-danger-alt">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert-alt alert-success-alt">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo $success; ?>
                        </div>
                        <div class="text-center mt-4 mb-3">
                            <p>Check your email for the password reset link. If you don't see it within a few minutes, check your spam folder.</p>
                            <div class="auth-divider">
                                <span>or</span>
                            </div>
                            <a href="<?php echo BASE_URL; ?>?page=login-alternative" class="btn-alt btn-outline-alt mt-3">
                                <i class="fas fa-sign-in-alt me-2"></i> Back to Login
                            </a>
                        </div>
                    <?php else: ?>
                        <form method="post">
                            <div class="form-group-alt">
                                <label for="email">Email Address</label>
                                <div class="input-group-alt">
                                    <input type="email" class="form-control-alt" id="email" name="email" placeholder="Enter your registered email address" required>
                                    <span class="input-group-icon">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn-alt btn-primary-alt btn-block-alt">
                                <i class="fas fa-paper-plane me-2"></i> Send Reset Instructions
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
                  <div class="auth-footer-alt">
                    <p>
                        Remember your password? 
                        <a href="<?php echo BASE_URL; ?>?page=login-alternative" class="auth-link fw-bold">
                            Sign In
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>    <!-- JavaScript Files -->
    <script src="<?php echo BASE_URL; ?>assets/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/theme-switcher.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/navigation-helper.js"></script>
</body>
</html>
