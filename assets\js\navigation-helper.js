/**
 * Navigation helper script for alternative pages
 * Fixes menu navigation in alternative pages and ensures proper URLs
 */

document.addEventListener('DOMContentLoaded', function() {
    // Fix navigation links in alternative pages
    fixNavigationLinks();
    
    // Add proper active classes to navigation
    highlightActiveNavItem();
    
    // Initialize mobile menu toggle
    initMobileMenu();
    
    // Fix login and register links in all pages
    fixAuthLinks();
});

/**
 * Fix navigation links to ensure they point to the correct alternative pages
 */
function fixNavigationLinks() {
    // Get the current page from URL
    const urlParams = new URLSearchParams(window.location.search);
    const currentPage = urlParams.get('page') || 'landing';
    const isAlternative = currentPage.includes('-alternative') || 
                         window.location.pathname.includes('-alternative');
    
    // Only process if we're on an alternative page
    if (!isAlternative) {
        return;
    }
    
    // Get all navigation links
    const navLinks = document.querySelectorAll('nav a');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        
        // Skip links that are anchors or external links
        if (!href || href.startsWith('#') || href.startsWith('http')) {
            return;
        }
        
        // If it's a page parameter link
        if (href.includes('?page=')) {
            const targetPage = href.split('?page=')[1].split('&')[0];
            
            // If the target page doesn't have -alternative suffix and it's not a dashboard/admin page
            if (!targetPage.includes('-alternative') && 
                !['dashboard', 'logout', 'admin', 'profile'].includes(targetPage)) {
                
                // Update the link to point to the alternative version
                link.setAttribute('href', href.replace(`?page=${targetPage}`, `?page=${targetPage}-alternative`));
            }
        }
        // If it's a direct file link (like index.php)
        else if (href.includes('.php') && !href.includes('-alternative')) {
            // Update direct file links
            const fileName = href.split('/').pop();
            const baseName = fileName.replace('.php', '');
            
            if (['index', 'login', 'register', 'forgot-password'].includes(baseName)) {
                link.setAttribute('href', href.replace(fileName, `${baseName}-alternative.php`));
            }
        }
    });
}

/**
 * Highlight the active navigation item
 */
function highlightActiveNavItem() {
    // Get the current page from URL
    const urlParams = new URLSearchParams(window.location.search);
    const currentPage = urlParams.get('page') || 'landing';
    
    // Also check pathname for standalone pages
    const pathname = window.location.pathname;
    let pageFromPath = '';
    
    if (pathname.includes('/')) {
        pageFromPath = pathname.split('/').pop().replace('.php', '');
    }
    
    // Get all navigation links
    const navLinks = document.querySelectorAll('nav a');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        
        // Skip if no href
        if (!href) {
            return;
        }
        
        // Check if link points to current page
        const pointsToCurrentPage = (href.includes(`?page=${currentPage}`) || 
                                    (pageFromPath && href.includes(pageFromPath)));
        
        // Add active class if link points to current page
        if (pointsToCurrentPage) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

/**
 * Initialize mobile menu toggle functionality
 */
function initMobileMenu() {
    const mobileToggle = document.querySelector('.navbar-toggler');
    
    if (mobileToggle) {
        mobileToggle.addEventListener('click', function() {
            const targetId = mobileToggle.getAttribute('data-bs-target');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                // Toggle the show class
                targetElement.classList.toggle('show');
            }
        });
    }
}

/**
 * Fix login and register links to ensure they point to the alternative versions
 * Now alternative versions are the default
 */
function fixAuthLinks() {
    // Get the current page from URL
    const urlParams = new URLSearchParams(window.location.search);
    const currentPage = urlParams.get('page') || 'landing-alternative';
    const isOriginal = !currentPage.includes('-alternative') && 
                      !window.location.pathname.includes('-alternative');
    
    // Get login and register links
    const authLinks = document.querySelectorAll('a[href*="login"], a[href*="register"]');
    
    authLinks.forEach(link => {
        const href = link.getAttribute('href');
        
        // Skip if no href
        if (!href) {
            return;
        }
        
        // In original pages (non-alternative), make links point to original pages
        if (isOriginal) {
            if (href.includes('?page=login-alternative')) {
                link.setAttribute('href', href.replace('?page=login-alternative', '?page=login'));
            }
            else if (href.includes('?page=register-alternative')) {
                link.setAttribute('href', href.replace('?page=register-alternative', '?page=register'));
            }
            else if (href === 'login-alternative.php') {
                link.setAttribute('href', 'login.php');
            }
            else if (href === 'register-alternative.php') {
                link.setAttribute('href', 'register.php');
            }
        } 
        // In alternative pages (now default), make sure links point to alternative pages
        else {
            if (href.includes('?page=login') && !href.includes('-alternative')) {
                link.setAttribute('href', href.replace('?page=login', '?page=login-alternative'));
            }
            else if (href.includes('?page=register') && !href.includes('-alternative')) {
                link.setAttribute('href', href.replace('?page=register', '?page=register-alternative'));
            }
            else if (href === 'login.php') {
                link.setAttribute('href', 'login-alternative.php');
            }
            else if (href === 'register.php') {
                link.setAttribute('href', 'register-alternative.php');
            }
        }
    });
}
