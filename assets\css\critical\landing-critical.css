/* Critical CSS for Above-the-fold Landing Page Content */

/* Base Styles */
:root {
  --primary-color: #4361ee;
  --primary-dark: #3a56d4;
  --primary-light: #4895ef;
  --secondary-color: #4cc9f0;
  --accent-color: #7209b7;
  --accent-warm: #ff9e00; /* New warm accent color (orange) */
  --accent-warm-dark: #e67700; /* Darker version for hover states */
  --dark-color: #1a1a2e;
  --light-color: #f8f9fa;
  --text-color: #333333;
  --text-light: #f1f1f1;
  --text-muted: #6c757d;
  --gradient-1: linear-gradient(135deg, #3a56d4, #4895ef);
  --gradient-4: linear-gradient(to right, #4361ee, #3a0ca3, #7209b7);
  --gradient-warm: linear-gradient(135deg, var(--accent-warm), var(--accent-warm-dark));
}

body.landing-page {
  font-family: 'Poppins', sans-serif;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  line-height: 1.7;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', 'Poppins', sans-serif;
  letter-spacing: -0.02em;
}

p, li, .btn, input, textarea, select {
  font-family: 'Poppins', sans-serif;
}

/* Navigation */
.navbar.bg-transparent {
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 1030;
  padding-top: 20px;
  padding-bottom: 20px;
  transition: all 0.4s ease;
  box-shadow: none;
}

.navbar.bg-transparent.scrolled {
  position: fixed;
  background-color: rgba(58, 86, 212, 0.95) !important;
  padding-top: 12px;
  padding-bottom: 12px;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}

.navbar.bg-transparent .navbar-brand {
  color: white;
  font-weight: 600;
}

.navbar.bg-transparent .nav-link {
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  margin: 0 0.25rem;
}

.navbar.bg-transparent .nav-link:hover,
.navbar.bg-transparent .nav-link:focus {
  color: white;
}

.navbar.bg-transparent .btn {
  padding: 0.5rem 1.25rem;
  margin-left: 0.5rem;
}

/* Hero Section - Critical Styles */
.hero-section {
  position: relative;
  background: var(--gradient-4);
  color: white;
  padding: 120px 0 180px;
  min-height: 85vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
      radial-gradient(circle at 20% 30%, rgba(76, 201, 240, 0.4) 0%, transparent 30%),
      radial-gradient(circle at 80% 70%, rgba(114, 9, 183, 0.4) 0%, transparent 30%);
  opacity: 0.8;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  width: 100%;
}

.text-gradient {
  background: var(--gradient-1);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.hero-text h1 {
  margin-bottom: 1rem;
  font-weight: 700;
  font-size: 3.5rem;
}

.hero-text h2 {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
}

.hero-text p {
  margin-bottom: 2rem;
  font-size: 1.1rem;
  opacity: 0.9;
}

.hero-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.btn-primary {
  background: var(--gradient-1);
  border-color: var(--primary-color);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.btn-outline-light {
  color: white;
  border-color: rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-outline-light:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Hero Shape */
.hero-shape {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}

/* Responsive Critical Styles */
@media (max-width: 991.98px) {
  .hero-section {
    padding: 100px 0 140px;
    min-height: auto;
  }

  .hero-text {
    text-align: center;
    margin-bottom: 40px;
  }

  .hero-text h1 {
    font-size: 3rem;
  }

  .hero-text p {
    max-width: 100%;
    margin-left: auto;
    margin-right: auto;
  }

  .navbar.bg-transparent {
    background-color: rgba(67, 97, 238, 0.98) !important;
    padding-top: 10px;
    padding-bottom: 10px;
    position: fixed;
  }

  body.landing-page {
    padding-top: 0 !important;
  }

  .navbar-collapse {
    background-color: rgba(67, 97, 238, 0.98);
    border-radius: 0 0 10px 10px;
    padding: 10px;
    margin: 0 -12px;
  }
}

@media (max-width: 767.98px) {
  .hero-section {
    padding: 80px 0 120px;
  }

  .hero-text h1 {
    font-size: 2.5rem;
  }

  .hero-buttons {
    flex-direction: column;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }

  .hero-buttons .btn {
    width: 100%;
    margin-bottom: 10px;
  }
}

@media (max-width: 575.98px) {
  .hero-section {
    padding: 80px 0 100px;
  }

  .hero-text h1 {
    font-size: 2rem;
  }

  .hero-text h2 {
    font-size: 1.2rem;
  }
}