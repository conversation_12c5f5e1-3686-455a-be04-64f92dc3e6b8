<?php
// Process forgot password form
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';

    if (empty($email)) {
        $error = "Email is required";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } else {
        // Check if email exists
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$email]);

        if ($stmt->rowCount() === 0) {
            $error = "No account found with that email address";
        } else {
            // In a real application, you would generate a token, save it to the database,
            // and send a password reset email to the user.
            // For this example, we'll just show a success message.
            $success = "If an account exists with that email address, you will receive password reset instructions.";
        }
    }
}
?>

<div class="back-to-home">
    <a href="<?php echo BASE_URL; ?>"><i class="fas fa-arrow-left"></i> Back to Home</a>
</div>

<div class="auth-card">
    <div class="card-header">
        <h4 class="mb-0">Reset Your Password</h4>
    </div>
    <div class="card-body">
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success"><?php echo $success; ?></div>
            <div class="text-center mt-4">
                <a href="<?php echo BASE_URL; ?>?page=login" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i> Return to Login
                </a>
            </div>
        <?php else: ?>
            <p class="mb-4">Enter your email address and we'll send you instructions to reset your password.</p>

            <form method="post">
                <div class="mb-4">
                    <label for="email" class="form-label">Email Address</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                        <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email address" required>
                    </div>
                </div>

                <div class="d-grid gap-2 mb-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-key me-2"></i> Reset Password
                    </button>
                </div>
            </form>
        <?php endif; ?>

        <div class="text-center">
            <p class="mb-0">
                Remember your password? <a href="<?php echo BASE_URL; ?>?page=login" class="fw-bold"><i class="fas fa-sign-in-alt me-1"></i> Back to Login</a>
            </p>
        </div>
    </div>
</div>
