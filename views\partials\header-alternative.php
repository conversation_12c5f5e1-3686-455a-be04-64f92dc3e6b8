<?php
// Page loading start time - for performance measurement
$pageLoadStartTime = microtime(true);

// Get app name from settings
$appName = getSetting('app_name', 'TimeTracker Pro');

// Check if user is logged in
$isLoggedIn = isLoggedIn();

// Set default page title
$pageTitle = isset($pageTitle) ? $pageTitle . ' | ' . $appName : $appName;

// Set meta description
$metaDescription = isset($metaDescription) ? $metaDescription : 'Track time, boost productivity, and simplify billing with ' . $appName . '. The next-generation time tracking solution for teams and businesses of all sizes.';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?><?php echo isset($_SESSION['company_name']) ? ' - ' . $_SESSION['company_name'] : ''; ?></title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="<?php echo $metaDescription; ?>">
    <meta name="keywords" content="time tracking, project management, productivity, billing, timesheet, team management">
    <meta name="author" content="<?php echo getSetting('company_name', 'TimeTracker Inc.'); ?>">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo BASE_URL; ?>">
    <meta property="og:title" content="<?php echo $pageTitle; ?>">
    <meta property="og:description" content="<?php echo $metaDescription; ?>">
    <meta property="og:image" content="<?php echo BASE_URL; ?>assets/img/og-image.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo BASE_URL; ?>">
    <meta property="twitter:title" content="<?php echo $pageTitle; ?>">
    <meta property="twitter:description" content="<?php echo $metaDescription; ?>">
    <meta property="twitter:image" content="<?php echo BASE_URL; ?>assets/img/twitter-image.jpg">

    <!-- Preload Fonts with font-display strategy -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@500;600;700;800&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@500;600;700;800&display=swap" rel="stylesheet"></noscript>

    <!-- Font-Display Fallback -->
    <style>
        @font-face {
            font-family: 'Inter Fallback';
            font-style: normal;
            font-weight: 400;
            src: local('Arial');
            font-display: swap;
        }
        @font-face {
            font-family: 'Montserrat Fallback';
            font-style: normal;
            font-weight: 700;
            src: local('Arial Bold');
            font-display: swap;
        }
        html { font-family: 'Inter Fallback', sans-serif; }
        h1, h2, h3, h4, h5, h6 { font-family: 'Montserrat Fallback', sans-serif; }
    </style>

    <!-- Resource Hints for Third-Party Domains -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>

    <!-- Bootstrap CSS -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"></noscript>    <!-- Font Awesome -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"></noscript>    <!-- Theme Switcher -->
    <script src="<?php echo BASE_URL; ?>assets/js/theme-switcher.js"></script>
    
    <!-- Navigation Helper -->
    <script src="<?php echo BASE_URL; ?>assets/js/navigation-helper.js"></script>
    
    <!-- Navbar Script -->
    <script src="<?php echo BASE_URL; ?>assets/js/navbar-alternative.js"></script>    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/style.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/landing-alternative.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/navbar-alternative.css">
    <?php if (strpos($page, 'login') !== false || strpos($page, 'register') !== false || strpos($page, 'forgot-password') !== false || strpos($page, 'reset-password') !== false): ?>
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/auth-alternative.css">
    <?php endif; ?>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo BASE_URL; ?>assets/img/favicon.ico">
</head>
<body class="landing-alternative-page">
    <!-- Skip Navigation Link for Accessibility -->
    <a href="#main-content" class="skip-link visually-hidden-focusable">Skip to main content</a>

    <!-- Navigation -->
    <header>
        <nav id="landingNavbarAlt" class="navbar navbar-expand-lg navbar-dark fixed-top navbar-alt <?php echo $isLoggedIn ? 'navbar-scrolled-alt' : 'navbar-transparent-alt'; ?>">            <div class="container">                <a class="navbar-brand navbar-brand-alt" href="<?php echo BASE_URL; ?><?php echo $isLoggedIn ? '?page=dashboard' : ($page === 'landing-alternative' ? 'landing-alternative.php' : ''); ?>">
                    <?php 
                    $logoPath = dirname(dirname(dirname(__FILE__))) . '/assets/img/logo-white.png';
                    if (file_exists($logoPath)): 
                    ?>
                    <img src="<?php echo BASE_URL; ?>assets/img/logo-white.png" alt="<?php echo $appName; ?>" height="40">
                    <?php else: ?>
                    <?php echo $appName; ?>
                    <?php endif; ?>
                </a>
                <button class="navbar-toggler navbar-toggler-alt" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavAlt" aria-controls="navbarNavAlt" aria-expanded="false" aria-label="Toggle navigation">
                    <i class="fas fa-bars"></i>
                </button><div class="collapse navbar-collapse navbar-collapse-alt" id="navbarNavAlt">
                    <ul class="navbar-nav navbar-nav-alt ms-auto">
                        <?php if ($page === 'landing-alternative' || $page === 'landing'): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="#home">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#features">Features</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#how-it-works">How It Works</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#testimonials">Testimonials</a>
                        </li>                        <li class="nav-item">
                            <a class="nav-link" href="#pricing">Pricing</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>design-comparison.php">Compare Designs</a>
                        </li>
                        <?php endif; ?>
                        <?php if (!$isLoggedIn): ?>
                        <li class="nav-item ms-lg-3">
                            <a href="<?php echo BASE_URL; ?>?page=login-alternative" class="nav-link px-4 py-2 btn btn-outline-light btn-sm rounded-pill">
                                <i class="fas fa-sign-in-alt me-2"></i>Login
                            </a>
                        </li>
                        <li class="nav-item mt-2 mt-lg-0 ms-lg-2">
                            <a href="<?php echo BASE_URL; ?>?page=register-alternative" class="nav-link px-4 py-2 btn btn-light text-primary btn-sm rounded-pill">
                                <i class="fas fa-user-plus me-2"></i>Sign Up
                            </a>
                        </li>
                        <?php else: ?>
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>?page=dashboard" class="nav-link">Dashboard</a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>?page=logout" class="nav-link">Logout</a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
