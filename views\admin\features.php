<?php
// Check if user is admin
if (!isAdmin()) {
    header('Location: ' . BASE_URL . '?page=dashboard');
    exit;
}

// Get company ID
$companyId = getCurrentCompanyId();

// Get available features for the company
$availableFeatures = getAvailableFeatures($companyId);

// Get company subscription
$stmt = $pdo->prepare("
    SELECT cs.*, sp.name as plan_name, sp.price as plan_price, sp.features
    FROM company_subscriptions cs
    JOIN subscription_plans sp ON cs.plan_id = sp.id
    WHERE cs.company_id = ? AND cs.status = 'active'
    ORDER BY cs.end_date DESC
    LIMIT 1
");
$stmt->execute([$companyId]);
$subscription = $stmt->fetch();
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3">Subscription Features</h1>
        <p class="text-muted">View your current subscription plan and available features</p>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Current Subscription</h5>
            </div>
            <div class="card-body">
                <?php if ($subscription): ?>
                    <div class="mb-4">
                        <h6 class="mb-3">Plan Details</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Plan:</strong> <span class="badge bg-info"><?php echo $subscription['plan_name']; ?></span></p>
                                <p class="mb-1"><strong>Price:</strong> $<?php echo number_format($subscription['plan_price'], 2); ?>/month</p>
                                <p class="mb-1"><strong>Status:</strong> 
                                    <?php if ($subscription['status'] === 'active'): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php elseif ($subscription['status'] === 'expired'): ?>
                                        <span class="badge bg-danger">Expired</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning text-dark">Cancelled</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Valid From:</strong> <?php echo formatDate($subscription['start_date'], 'M d, Y'); ?></p>
                                <p class="mb-1"><strong>Valid Until:</strong> <?php echo formatDate($subscription['end_date'], 'M d, Y'); ?></p>
                                <p class="mb-1">
                                    <strong>Days Remaining:</strong>
                                    <?php 
                                        $daysRemaining = (strtotime($subscription['end_date']) - time()) / (60 * 60 * 24);
                                        echo max(0, floor($daysRemaining));
                                    ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h6 class="mb-3">Available Features</h6>
                        <?php if (!empty($availableFeatures)): ?>
                            <div class="row">
                                <?php foreach ($availableFeatures as $feature): ?>
                                    <div class="col-md-6 mb-2">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-check-circle text-success me-2"></i>
                                            <?php echo $feature; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <p class="text-muted">No features available for your current plan.</p>
                        <?php endif; ?>
                    </div>
                    
                    <div>
                        <h6 class="mb-3">Feature Examples</h6>
                        <div class="list-group">
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">Advanced Reporting</h6>
                                    <?php if (hasFeatureAccess('Advanced reporting')): ?>
                                        <span class="badge bg-success">Available</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Unavailable</span>
                                    <?php endif; ?>
                                </div>
                                <p class="mb-1">Generate detailed reports with advanced filtering and visualization options.</p>
                                <?php if (!hasFeatureAccess('Advanced reporting')): ?>
                                    <small class="text-muted">Upgrade your plan to access this feature.</small>
                                <?php endif; ?>
                            </div>
                            
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">Export to CSV/PDF</h6>
                                    <?php if (hasFeatureAccess('Export to CSV/PDF')): ?>
                                        <span class="badge bg-success">Available</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Unavailable</span>
                                    <?php endif; ?>
                                </div>
                                <p class="mb-1">Export your timesheet data to CSV or PDF formats for external use.</p>
                                <?php if (!hasFeatureAccess('Export to CSV/PDF')): ?>
                                    <small class="text-muted">Upgrade your plan to access this feature.</small>
                                <?php endif; ?>
                            </div>
                            
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">API Access</h6>
                                    <?php if (hasFeatureAccess('API access')): ?>
                                        <span class="badge bg-success">Available</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Unavailable</span>
                                    <?php endif; ?>
                                </div>
                                <p class="mb-1">Access our API to integrate with other systems and automate workflows.</p>
                                <?php if (!hasFeatureAccess('API access')): ?>
                                    <small class="text-muted">Upgrade your plan to access this feature.</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No active subscription found for your company. Please contact your administrator.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Need More Features?</h5>
            </div>
            <div class="card-body">
                <p>Upgrade your subscription plan to access more features and increase your limits.</p>
                <p>Contact your administrator or our support team to discuss upgrade options.</p>
                <div class="d-grid gap-2">
                    <a href="mailto:<?php echo getSetting('support_email', '<EMAIL>'); ?>" class="btn btn-primary">
                        <i class="fas fa-envelope me-2"></i> Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
