# Enable URL rewriting
RewriteEngine On

# Set the base directory
RewriteBase /

# Prevent direct access to PHP files in the includes directory
RewriteRule ^includes/ - [F,L]
RewriteRule ^config/ - [F,L]

# Prevent direct access to .htaccess
<Files .htaccess>
    Order Allow,<PERSON><PERSON> from all
</Files>

# Prevent directory listing
Options -Indexes

# Set default character set
AddDefaultCharset UTF-8

# Set default index file
DirectoryIndex index.php

# PHP settings
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300

# Security headers
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>
