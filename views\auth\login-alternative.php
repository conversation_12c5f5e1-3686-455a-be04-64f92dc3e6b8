<?php
// This template contains only the login form content
// The header and footer are included by the parent template
?>

<!-- Alternative Login Section -->
<section class="login-alternative-section py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card login-card shadow-lg border-0 rounded-lg mt-5">
                    <div class="card-header bg-gradient-primary-alt text-white text-center py-4">
                        <h2 class="fw-light mb-0">Login to TimeTracker</h2>
                    </div>
                    <div class="card-body p-5">
                        <?php if (!empty($error)): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        
                        <form action="" method="post">
                            <div class="form-floating mb-4">
                                <input class="form-control rounded-3" id="username" type="text" name="username" placeholder="Userna<PERSON> or Email" required />
                                <label for="username">Userna<PERSON> or Email</label>
                            </div>
                            
                            <div class="form-floating mb-4">
                                <input class="form-control rounded-3" id="password" type="password" name="password" placeholder="Password" required />
                                <label for="password">Password</label>
                            </div>
                            
                            <div class="form-check mb-4">
                                <input class="form-check-input" id="remember" type="checkbox" name="remember" value="1" />
                                <label class="form-check-label" for="remember">Remember me</label>
                            </div>
                            
                            <div class="d-grid">
                                <button class="btn btn-gradient-primary-alt btn-lg rounded-3 text-white" type="submit">
                                    <i class="fas fa-sign-in-alt me-2"></i>Login
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer text-center py-4">
                        <div class="mb-3">
                            <a class="small" href="<?php echo BASE_URL; ?>?page=forgot-password-alternative">Forgot your password?</a>
                        </div>
                        <div>
                            Don't have an account? 
                            <a href="<?php echo BASE_URL; ?>?page=register-alternative">Sign up now</a>
                        </div>
                        <div class="mt-3">
                            <a href="<?php echo BASE_URL; ?>?page=login" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-palette me-1"></i>Try original design
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Include navigation helper script -->
<script src="<?php echo BASE_URL; ?>assets/js/navigation-helper.js"></script>
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/fontawesome.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/auth-alternative.css">
    
    <!-- Favicon -->
    <link rel="icon" href="<?php echo BASE_URL; ?>assets/img/favicon.png">
</head>
<body>    <div class="auth-page-alt">
        <div class="back-to-home-alt">
            <a href="<?php echo BASE_URL; ?>landing-alternative.php">
                <i class="fas fa-arrow-left"></i> Back to Home
            </a>
        </div>
        
        <div class="auth-nav-alt">
            <?php if (getSetting('allow_staff_registration', '1') == '1' || getSetting('allow_contractor_registration', '1') == '1'): ?>
            <a href="<?php echo BASE_URL; ?>?page=register-alternative">
                <i class="fas fa-user-plus"></i> Register
            </a>
            <?php endif; ?>
            <a href="<?php echo BASE_URL; ?>design-comparison.php">
                <i class="fas fa-palette"></i> Designs
            </a>
        </div>
        
        <div class="container">
            <div class="auth-card-alt">
                <div class="auth-header-alt">
                    <div class="brand text-center mb-4">
                        <img src="<?php echo BASE_URL; ?>assets/img/logo.png" alt="TimeTracker Logo" height="40">
                    </div>
                    <h3>Welcome Back</h3>
                    <p>Sign in to your TimeTracker account</p>
                </div>
                  <div class="auth-body-alt">
                    <?php if (isset($error) && !empty($error)): ?>
                        <div class="alert-alt alert-danger-alt">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="post">
                        <div class="form-group-alt">
                            <label for="username">Username or Email</label>
                            <div class="input-group-alt">
                                <input type="text" class="form-control-alt" id="username" name="username" placeholder="Enter your username or email" required>
                                <span class="input-group-icon">
                                    <i class="fas fa-user"></i>
                                </span>
                            </div>
                        </div>
                        
                        <div class="form-group-alt">
                            <label for="password">Password</label>
                            <div class="input-group-alt">
                                <input type="password" class="form-control-alt" id="password" name="password" placeholder="Enter your password" required>
                                <span class="input-group-icon">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <span class="password-toggle" id="togglePassword">
                                    <i class="far fa-eye"></i>
                                </span>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="rememberMe">
                                <label class="form-check-label" for="rememberMe">Remember me</label>
                            </div>
                            <a href="<?php echo BASE_URL; ?>?page=forgot-password-alternative" class="auth-link">
                                Forgot Password?
                            </a>
                        </div>
                        
                        <button type="submit" class="btn-alt btn-primary-alt btn-block-alt">
                            <i class="fas fa-sign-in-alt me-2"></i> Sign In
                        </button>
                    </form>
                    
                    <div class="auth-divider">
                        <span>or continue with</span>
                    </div>
                    
                    <div class="social-buttons">
                        <button type="button" class="social-btn social-btn-google">
                            <i class="fab fa-google"></i> Google
                        </button>
                        <button type="button" class="social-btn social-btn-facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </button>
                    </div>
                </div>
                  <div class="auth-footer-alt">
                    <?php if (getSetting('allow_staff_registration', '1') == '1' || getSetting('allow_contractor_registration', '1') == '1'): ?>
                        <p>
                            Don't have an account yet? 
                            <a href="<?php echo BASE_URL; ?>?page=register-alternative" class="auth-link fw-bold">
                                Create an account
                            </a>
                        </p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>    <!-- JavaScript Files -->
    <script src="<?php echo BASE_URL; ?>assets/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/theme-switcher.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/navigation-helper.js"></script>
    <script>
        // Password visibility toggle
        document.addEventListener('DOMContentLoaded', function() {
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');
            
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                // Toggle eye icon
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });
        });
    </script>
</body>
</html>
