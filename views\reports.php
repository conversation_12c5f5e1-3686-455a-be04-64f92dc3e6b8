<?php
// Get current user
$currentUser = getCurrentUser();

// Get date range from query string or use current month
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-t');

// Validate date format
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $startDate)) {
    $startDate = date('Y-m-01');
}
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $endDate)) {
    $endDate = date('Y-m-t');
}

// Get project filter from query string
$projectFilter = isset($_GET['project_id']) ? (int)$_GET['project_id'] : 0;

// Get all projects for filter dropdown
$stmt = $pdo->query("SELECT * FROM projects ORDER BY name");
$projects = $stmt->fetchAll();

// Build query based on user role and filters
$params = [];
$sql = "
    SELECT t.*, p.name as project_name, u.first_name, u.last_name, u.username
    FROM timesheets t
    JOIN projects p ON t.project_id = p.id
    JOIN users u ON t.user_id = u.id
    WHERE t.date BETWEEN ? AND ?
";
$params[] = $startDate;
$params[] = $endDate;

// Add project filter if selected
if ($projectFilter > 0) {
    $sql .= " AND t.project_id = ?";
    $params[] = $projectFilter;
}

// Restrict to current user if not admin
if (!isAdmin()) {
    $sql .= " AND t.user_id = ?";
    $params[] = $currentUser['id'];
}

$sql .= " ORDER BY t.date DESC, p.name ASC";

// Execute query
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$timesheets = $stmt->fetchAll();

// Calculate totals
$totalHours = 0;
$projectHours = [];
$userHours = [];
$dailyHours = [];

foreach ($timesheets as $timesheet) {
    $totalHours += $timesheet['hours'];

    // Project totals
    if (!isset($projectHours[$timesheet['project_id']])) {
        $projectHours[$timesheet['project_id']] = [
            'name' => $timesheet['project_name'],
            'hours' => 0
        ];
    }
    $projectHours[$timesheet['project_id']]['hours'] += $timesheet['hours'];

    // User totals (for admins)
    if (isAdmin()) {
        $userId = $timesheet['user_id'];
        if (!isset($userHours[$userId])) {
            $userHours[$userId] = [
                'name' => $timesheet['first_name'] . ' ' . $timesheet['last_name'],
                'username' => $timesheet['username'],
                'hours' => 0
            ];
        }
        $userHours[$userId]['hours'] += $timesheet['hours'];
    }

    // Daily totals
    $date = $timesheet['date'];
    if (!isset($dailyHours[$date])) {
        $dailyHours[$date] = 0;
    }
    $dailyHours[$date] += $timesheet['hours'];
}

// Sort project hours by total hours (descending)
uasort($projectHours, function($a, $b) {
    return $b['hours'] <=> $a['hours'];
});

// Sort user hours by total hours (descending)
uasort($userHours, function($a, $b) {
    return $b['hours'] <=> $a['hours'];
});

// Sort daily hours by date (ascending)
ksort($dailyHours);
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3">Reports</h1>
        <p class="text-muted">View and analyze time entries</p>
    </div>
    <div class="col-md-6 text-md-end">
        <button type="button" class="btn btn-outline-primary btn-print">
            <i class="fas fa-print"></i> Print Report
        </button>

        <?php if (hasFeatureAccess('Export to CSV/PDF')): ?>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-success btn-export" data-table-id="timesheetTable">
                <i class="fas fa-file-csv"></i> Export CSV
            </button>
            <button type="button" class="btn btn-outline-danger">
                <i class="fas fa-file-pdf"></i> Export PDF
            </button>
        </div>
        <?php else: ?>
        <button type="button" class="btn btn-outline-secondary" data-bs-toggle="tooltip" data-bs-placement="bottom" title="Upgrade your plan to access export features">
            <i class="fas fa-file-export"></i> Export
            <i class="fas fa-lock-alt small ms-1"></i>
        </button>
        <?php endif; ?>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">Report Filters</h5>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <input type="hidden" name="page" value="reports">

            <div class="col-md-4">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $startDate; ?>">
            </div>

            <div class="col-md-4">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $endDate; ?>">
            </div>

            <div class="col-md-4">
                <label for="project_id" class="form-label">Project</label>
                <select class="form-select" id="project_id" name="project_id">
                    <option value="0">All Projects</option>
                    <?php foreach ($projects as $project): ?>
                        <option value="<?php echo $project['id']; ?>" <?php echo $projectFilter == $project['id'] ? 'selected' : ''; ?>>
                            <?php echo $project['name']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="col-12">
                <button type="submit" class="btn btn-primary">Apply Filters</button>
                <a href="<?php echo BASE_URL; ?>?page=reports" class="btn btn-outline-secondary">Reset</a>
            </div>
        </form>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Time Entries</h5>
                <span class="badge bg-primary">Total: <?php echo number_format($totalHours, 2); ?> hours</span>
            </div>
            <div class="card-body">
                <?php if (empty($timesheets)): ?>
                    <p class="text-muted">No time entries found for the selected period.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover" id="timesheetTable">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Project</th>
                                    <?php if (isAdmin()): ?>
                                        <th>User</th>
                                    <?php endif; ?>
                                    <th>Hours</th>
                                    <th>Description</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($timesheets as $timesheet): ?>
                                    <tr>
                                        <td><?php echo formatDate($timesheet['date'], 'M d, Y'); ?></td>
                                        <td><?php echo $timesheet['project_name']; ?></td>
                                        <?php if (isAdmin()): ?>
                                            <td><?php echo $timesheet['first_name'] . ' ' . $timesheet['last_name']; ?></td>
                                        <?php endif; ?>
                                        <td><?php echo number_format($timesheet['hours'], 2); ?></td>
                                        <td><?php echo $timesheet['description'] ? $timesheet['description'] : '<em>No description</em>'; ?></td>
                                        <td>
                                            <?php if ($timesheet['status'] === 'approved'): ?>
                                                <span class="badge bg-success">Approved</span>
                                            <?php elseif ($timesheet['status'] === 'rejected'): ?>
                                                <span class="badge bg-danger">Rejected</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning text-dark">Pending</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Summary</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>Date Range</h6>
                    <p class="mb-0">
                        <?php echo formatDate($startDate, 'F j, Y'); ?> -
                        <?php echo formatDate($endDate, 'F j, Y'); ?>
                    </p>
                </div>

                <div class="mb-3">
                    <h6>Total Hours</h6>
                    <p class="display-6 mb-0"><?php echo number_format($totalHours, 2); ?></p>
                </div>

                <?php if (isAdmin() && !empty($userHours)): ?>
                    <div class="mb-3">
                        <h6>Hours by User</h6>
                        <div class="list-group">
                            <?php foreach (array_slice($userHours, 0, 5) as $userId => $user): ?>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0"><?php echo $user['name']; ?></h6>
                                        <small class="text-muted"><?php echo $user['username']; ?></small>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">
                                        <?php echo number_format($user['hours'], 2); ?> hrs
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($projectHours)): ?>
                    <div class="mb-3">
                        <h6>Hours by Project</h6>
                        <div class="list-group">
                            <?php foreach (array_slice($projectHours, 0, 5) as $projectId => $project): ?>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0"><?php echo $project['name']; ?></h6>
                                    </div>
                                    <span class="badge bg-primary rounded-pill">
                                        <?php echo number_format($project['hours'], 2); ?> hrs
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (hasFeatureAccess('Advanced reporting')): ?>
                <!-- Advanced Reporting Features -->
                <div class="mb-3">
                    <h6>Advanced Analytics</h6>
                    <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">Detailed Breakdown</h6>
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <p class="mb-1 small">View detailed time distribution by project and user</p>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">Trend Analysis</h6>
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <p class="mb-1 small">View time tracking trends over longer periods</p>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">Custom Reports</h6>
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <p class="mb-1 small">Create and save custom report templates</p>
                        </a>
                    </div>
                </div>
                <?php else: ?>
                <div class="alert alert-info mt-3">
                    <h6><i class="fas fa-star me-2"></i>Advanced Reporting</h6>
                    <p class="small mb-0">Upgrade your subscription plan to access advanced reporting features including detailed breakdowns, trend analysis, and custom reports.</p>
                    <div class="mt-2">
                        <a href="<?php echo BASE_URL; ?>?page=admin-features" class="btn btn-sm btn-outline-primary">View Features</a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Print button functionality
    document.querySelector('.btn-print').addEventListener('click', function() {
        window.print();
    });

    <?php if (hasFeatureAccess('Export to CSV/PDF')): ?>
    // Export CSV functionality
    document.querySelector('.btn-export').addEventListener('click', function() {
        const tableId = this.getAttribute('data-table-id');
        exportTableToCSV(tableId);
    });

    // Function to export table to CSV
    function exportTableToCSV(tableId) {
        const table = document.getElementById(tableId);
        if (!table) return;

        let csv = [];
        const rows = table.querySelectorAll('tr');

        for (let i = 0; i < rows.length; i++) {
            const row = [], cols = rows[i].querySelectorAll('td, th');

            for (let j = 0; j < cols.length; j++) {
                // Replace HTML with plain text and escape quotes
                let data = cols[j].innerText.replace(/(\r\n|\n|\r)/gm, '').replace(/"/g, '""');
                row.push('"' + data + '"');
            }

            csv.push(row.join(','));
        }

        // Download CSV file
        downloadCSV(csv.join('\n'), 'timesheet_report.csv');
    }

    function downloadCSV(csv, filename) {
        const csvFile = new Blob([csv], {type: 'text/csv'});
        const downloadLink = document.createElement('a');

        // Create a download link
        downloadLink.download = filename;
        downloadLink.href = window.URL.createObjectURL(csvFile);
        downloadLink.style.display = 'none';

        // Add to DOM, trigger click, and remove
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
    }
    <?php endif; ?>
});
</script>