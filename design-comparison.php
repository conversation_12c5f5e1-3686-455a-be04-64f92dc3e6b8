<?php
// This file demonstrates the comparison between original and alternative designs
require_once 'config/session.php';
session_start();

// Include other configuration files
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';
require_once 'includes/alternative-routes.php';

// Define the base URL
define('BASE_URL', getBaseUrl());

// Set page variables
$page = 'design-comparison';
$pageTitle = 'Design Comparison';
$metaDescription = 'Compare the original and alternative designs of TimeTracker Pro';

// Start output buffering to capture all output
ob_start();

// Include the header
require_once 'views/partials/header-alternative.php';
?>

<main id="main-content" class="pt-5">
    <div class="container mt-5 pt-5">
        <div class="row mb-5">
            <div class="col-lg-12 text-center">
                <h1 class="display-4 mb-4">Design Comparison</h1>
                <p class="lead mb-5">Compare the original and alternative designs of TimeTracker Pro</p>
                
                <div class="d-flex justify-content-center mb-5">
                    <button id="switch-to-original" class="btn btn-outline-primary me-3">View Original Design</button>
                    <button id="switch-to-alternative" class="btn btn-primary">View Alternative Design</button>
                </div>
            </div>
        </div>
        
        <div class="row mb-5">
            <div class="col-lg-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Page Comparison</h5>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="splitViewToggle">
                            <label class="form-check-label" for="splitViewToggle">Split View</label>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="comparison-container" class="row">
                            <div class="col-md-6 comparison-column">
                                <h5 class="text-center mb-4">Original Design</h5>
                                <div class="comparison-frame">
                                    <img src="<?php echo BASE_URL; ?>assets/img/screenshots/original-landing.jpg" alt="Original Landing Page" class="img-fluid rounded shadow-sm mb-3">
                                </div>
                                <div class="text-center mb-4">
                                    <a href="<?php echo BASE_URL; ?>" class="btn btn-sm btn-outline-primary">Visit Original Design</a>
                                </div>
                                <h6 class="fw-bold mt-4">Features:</h6>
                                <ul class="comparison-features">
                                    <li>Classic and simple user interface</li>
                                    <li>Straightforward navigation</li>
                                    <li>Familiar design patterns</li>
                                    <li>Standard form elements</li>
                                    <li>Consistent color scheme</li>
                                </ul>
                            </div>
                            
                            <div class="col-md-6 comparison-column">
                                <h5 class="text-center mb-4">Alternative Design</h5>
                                <div class="comparison-frame">
                                    <img src="<?php echo BASE_URL; ?>assets/img/screenshots/alternative-landing.jpg" alt="Alternative Landing Page" class="img-fluid rounded shadow-sm mb-3">
                                </div>
                                <div class="text-center mb-4">
                                    <a href="<?php echo BASE_URL; ?>landing-alternative.php" class="btn btn-sm btn-primary">Visit Alternative Design</a>
                                </div>
                                <h6 class="fw-bold mt-4">Features:</h6>
                                <ul class="comparison-features">
                                    <li>Modern and sophisticated design</li>
                                    <li>Enhanced visual hierarchy</li>
                                    <li>Smooth animations and transitions</li>
                                    <li>Improved form design with visual feedback</li>
                                    <li>Gradient and shadow effects</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-5">
            <div class="col-lg-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Pages Available in Alternative Design</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">                                <thead>
                                    <tr>
                                        <th>Page</th>
                                        <th>New Default Design</th>
                                        <th>Original Design</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Landing Page</td>
                                        <td><a href="<?php echo BASE_URL; ?>" class="btn btn-sm btn-primary">View</a></td>
                                        <td><a href="<?php echo BASE_URL; ?>?page=landing-original" class="btn btn-sm btn-outline-primary">View</a></td>
                                        <td><span class="badge bg-success">Complete</span></td>
                                    </tr>
                                    <tr>
                                        <td>Login</td>
                                        <td><a href="<?php echo BASE_URL; ?>?page=login" class="btn btn-sm btn-primary">View</a></td>
                                        <td><a href="<?php echo BASE_URL; ?>?page=login-original" class="btn btn-sm btn-outline-primary">View</a></td>
                                        <td><span class="badge bg-success">Complete</span></td>
                                    </tr>
                                    <tr>
                                        <td>Register</td>
                                        <td><a href="<?php echo BASE_URL; ?>?page=register" class="btn btn-sm btn-primary">View</a></td>
                                        <td><a href="<?php echo BASE_URL; ?>?page=register-original" class="btn btn-sm btn-outline-primary">View</a></td>
                                        <td><span class="badge bg-success">Complete</span></td>
                                    </tr>
                                    <tr>
                                        <td>Forgot Password</td>
                                        <td><a href="<?php echo BASE_URL; ?>?page=forgot-password" class="btn btn-sm btn-primary">View</a></td>
                                        <td><a href="<?php echo BASE_URL; ?>?page=forgot-password-original" class="btn btn-sm btn-outline-primary">View</a></td>
                                        <td><span class="badge bg-success">Complete</span></td>
                                    </tr>
                                    <tr>
                                        <td>Reset Password</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-secondary" disabled title="Requires token">View</button>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-secondary" disabled title="Requires token">View</button>
                                        </td>
                                        <td><span class="badge bg-success">Complete</span></td>
                                    </tr>
                                    <tr>
                                        <td>Dashboard</td>
                                        <td><a href="<?php echo BASE_URL; ?>?page=dashboard" class="btn btn-sm btn-outline-primary">View</a></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-secondary" disabled>Not Available</button>
                                        </td>
                                        <td><span class="badge bg-warning text-dark">Pending</span></td>
                                    </tr>
                                    <tr>
                                        <td>Timesheet</td>
                                        <td><a href="<?php echo BASE_URL; ?>?page=timesheet" class="btn btn-sm btn-outline-primary">View</a></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-secondary" disabled>Not Available</button>
                                        </td>
                                        <td><span class="badge bg-warning text-dark">Pending</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-5">
            <div class="col-lg-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">Design Comparison Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <h5>Original Design</h5>
                                <p>The original design focuses on simplicity and functionality with a straightforward approach to time tracking. It uses a traditional layout with standard Bootstrap components.</p>
                                
                                <h6 class="mt-4">Color Scheme:</h6>
                                <div class="d-flex flex-wrap">
                                    <div class="color-swatch" style="background-color: #007bff;"><span>#007bff</span></div>
                                    <div class="color-swatch" style="background-color: #6c757d;"><span>#6c757d</span></div>
                                    <div class="color-swatch" style="background-color: #28a745;"><span>#28a745</span></div>
                                    <div class="color-swatch" style="background-color: #dc3545;"><span>#dc3545</span></div>
                                    <div class="color-swatch" style="background-color: #ffc107; color: #212529;"><span>#ffc107</span></div>
                                </div>
                                
                                <h6 class="mt-4">Typography:</h6>
                                <ul>
                                    <li>Primary Font: Open Sans</li>
                                    <li>Headings Font: Open Sans (bold)</li>
                                    <li>Emphasis on readability</li>
                                </ul>
                            </div>
                            
                            <div class="col-md-6 mb-4">
                                <h5>Alternative Design</h5>
                                <p>The alternative design embraces modern web design trends with gradients, enhanced animations, and a more visually appealing interface while maintaining the same core functionality.</p>
                                
                                <h6 class="mt-4">Color Scheme:</h6>
                                <div class="d-flex flex-wrap">
                                    <div class="color-swatch" style="background-color: #4f46e5;"><span>#4f46e5</span></div>
                                    <div class="color-swatch" style="background-color: #10b981;"><span>#10b981</span></div>
                                    <div class="color-swatch" style="background-color: #8b5cf6;"><span>#8b5cf6</span></div>
                                    <div class="color-swatch" style="background-color: #1f2937;"><span>#1f2937</span></div>
                                    <div class="color-swatch" style="background-color: #f9fafb; color: #111827;"><span>#f9fafb</span></div>
                                </div>
                                
                                <h6 class="mt-4">Typography:</h6>
                                <ul>
                                    <li>Primary Font: Inter</li>
                                    <li>Headings Font: Montserrat</li>
                                    <li>Focus on visual hierarchy and spacing</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-lg-12">
                                <h5>Key Differences:</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Feature</th>
                                                <th>Original Design</th>
                                                <th>Alternative Design</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Navigation</td>
                                                <td>Standard navbar with traditional dropdown</td>
                                                <td>Enhanced navbar with scroll effects and improved mobile navigation</td>
                                            </tr>
                                            <tr>
                                                <td>Button Style</td>
                                                <td>Standard Bootstrap buttons</td>
                                                <td>Custom buttons with gradients and subtle animations</td>
                                            </tr>
                                            <tr>
                                                <td>Form Elements</td>
                                                <td>Standard form controls</td>
                                                <td>Enhanced form controls with better visual feedback</td>
                                            </tr>
                                            <tr>
                                                <td>Visual Effects</td>
                                                <td>Minimal use of shadows and transitions</td>
                                                <td>Extensive use of shadows, gradients, and animations</td>
                                            </tr>
                                            <tr>
                                                <td>Responsiveness</td>
                                                <td>Fully responsive standard layout</td>
                                                <td>Fully responsive with enhanced mobile experience</td>
                                            </tr>
                                            <tr>
                                                <td>Loading Speed</td>
                                                <td>Faster loading with minimal effects</td>
                                                <td>Slightly slower due to enhanced visual effects</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-5">
            <div class="col-lg-12 text-center">
                <div class="card shadow-sm">
                    <div class="card-body py-5">
                        <h3 class="mb-4">Which Design Do You Prefer?</h3>
                        <p class="mb-4">We value your feedback! Let us know which design you prefer by selecting one below:</p>
                        
                        <div class="d-flex justify-content-center flex-wrap">
                            <button id="vote-original" class="btn btn-lg btn-outline-primary m-2 px-4">
                                <i class="fas fa-thumbs-up me-2"></i> Original Design
                            </button>
                            <button id="vote-alternative" class="btn btn-lg btn-primary m-2 px-4">
                                <i class="fas fa-thumbs-up me-2"></i> Alternative Design
                            </button>
                        </div>
                        
                        <div id="vote-confirmation" class="mt-4 d-none alert alert-success">
                            Thanks for your feedback! Your preference has been recorded.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<style>
    .comparison-frame {
        border: 1px solid #e9ecef;
        border-radius: 0.5rem;
        padding: 0.5rem;
        background-color: #f8f9fa;
        height: 300px;
        overflow: hidden;
    }
    
    .comparison-frame img {
        width: 100%;
        object-fit: cover;
        object-position: top;
        height: 100%;
        transition: all 0.3s ease;
    }
    
    .comparison-frame img:hover {
        transform: translateY(-20px);
    }
    
    .comparison-features li {
        margin-bottom: 0.5rem;
    }
    
    .color-swatch {
        width: 80px;
        height: 40px;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.75rem;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    #comparison-container.split-view {
        display: flex;
        flex-wrap: wrap;
    }
    
    @media (max-width: 767.98px) {
        .comparison-frame {
            height: 200px;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Theme switcher buttons
        const switchToOriginal = document.getElementById('switch-to-original');
        const switchToAlternative = document.getElementById('switch-to-alternative');
        
        if (switchToOriginal) {
            switchToOriginal.addEventListener('click', function() {
                // Store preference
                localStorage.setItem('timeTracker.theme', 'original');
                
                // Redirect to original landing page
                window.location.href = '<?php echo BASE_URL; ?>';
            });
        }
        
        if (switchToAlternative) {
            switchToAlternative.addEventListener('click', function() {
                // Store preference
                localStorage.setItem('timeTracker.theme', 'alternative');
                
                // Redirect to alternative landing page
                window.location.href = '<?php echo BASE_URL; ?>landing-alternative.php';
            });
        }
        
        // Split view toggle
        const splitViewToggle = document.getElementById('splitViewToggle');
        const comparisonContainer = document.getElementById('comparison-container');
        
        if (splitViewToggle && comparisonContainer) {
            splitViewToggle.addEventListener('change', function() {
                if (this.checked) {
                    comparisonContainer.classList.add('split-view');
                } else {
                    comparisonContainer.classList.remove('split-view');
                }
            });
        }
        
        // Voting buttons
        const voteOriginal = document.getElementById('vote-original');
        const voteAlternative = document.getElementById('vote-alternative');
        const voteConfirmation = document.getElementById('vote-confirmation');
        
        if (voteOriginal && voteAlternative && voteConfirmation) {
            voteOriginal.addEventListener('click', function() {
                recordVote('original');
                voteConfirmation.classList.remove('d-none');
                setTimeout(() => {
                    voteConfirmation.classList.add('d-none');
                }, 3000);
            });
            
            voteAlternative.addEventListener('click', function() {
                recordVote('alternative');
                voteConfirmation.classList.remove('d-none');
                setTimeout(() => {
                    voteConfirmation.classList.add('d-none');
                }, 3000);
            });
        }
        
        // Record vote
        function recordVote(preference) {
            localStorage.setItem('timeTracker.design.vote', preference);
            console.log('Voted for:', preference);
            // In a real application, you would send this to the server
        }
    });
</script>

<?php
// Include the footer
require_once 'views/partials/footer-alternative.php';

// Output the buffered content
ob_end_flush();
?>
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .design-card {
            flex: 1;
            min-width: 300px;
            max-width: 500px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .design-card:hover {
            transform: translateY(-10px);
        }
        
        .design-card .card-header {
            padding: 20px;
            background-color: #4361ee;
            color: white;
            text-align: center;
        }
        
        .design-card.alternative .card-header {
            background-color: #4f46e5;
        }
        
        .design-card .card-body {
            padding: 20px;
        }
        
        .design-card img {
            width: 100%;
            height: auto;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #eee;
        }
        
        .design-card .btn {
            display: block;
            width: 100%;
            padding: 12px;
            border-radius: 5px;
            text-align: center;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .design-card .btn-primary {
            background-color: #4361ee;
            color: white;
            border: none;
        }
        
        .design-card.alternative .btn-primary {
            background-color: #4f46e5;
        }
        
        .design-card .btn-primary:hover {
            opacity: 0.9;
        }
        
        .design-card .features {
            margin: 20px 0;
        }
        
        .design-card .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .design-card .feature-item i {
            margin-right: 10px;
            color: #4361ee;
        }
        
        .design-card.alternative .feature-item i {
            color: #4f46e5;
        }
        
        .comparison-table {
            max-width: 1000px;
            margin: 0 auto 40px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .comparison-table th {
            background-color: #f8f9fa;
            padding: 15px;
            text-align: left;
        }
        
        .comparison-table td {
            padding: 15px;
            border-top: 1px solid #eee;
        }
        
        .comparison-table .check {
            color: #10b981;
        }
        
        @media (max-width: 768px) {
            .design-options {
                flex-direction: column;
                align-items: center;
            }
            
            .design-card {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>TimeTracker Design Options</h1>
            <p>Compare and choose your preferred TimeTracker interface design</p>
        </div>
        
        <div class="design-options">
            <div class="design-card">
                <div class="card-header">
                    <h2>Original Design</h2>
                    <p>Classic and functional interface</p>
                </div>
                <div class="card-body">
                    <img src="https://via.placeholder.com/450x300?text=Original+Design+Screenshot" alt="Original Design Screenshot">
                    
                    <div class="features">
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>Classic blue color scheme</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>Clean, functional layout</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>Standard form elements</span>
                        </div>
                    </div>
                    
                    <a href="index.php" class="btn btn-primary">View Original Design</a>
                </div>
            </div>
            
            <div class="design-card alternative">
                <div class="card-header">
                    <h2>Alternative Design</h2>
                    <p>Modern and vibrant interface</p>
                </div>
                <div class="card-body">
                    <img src="https://via.placeholder.com/450x300?text=Alternative+Design+Screenshot" alt="Alternative Design Screenshot">
                    
                    <div class="features">
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>Indigo and green color palette</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>Modern, animated UI elements</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check-circle"></i>
                            <span>Enhanced form interactions</span>
                        </div>
                    </div>
                    
                    <a href="landing-alternative.php" class="btn btn-primary">View Alternative Design</a>
                </div>
            </div>
        </div>
        
        <div class="comparison-table">
            <table>
                <thead>
                    <tr>
                        <th>Feature</th>
                        <th>Original Design</th>
                        <th>Alternative Design</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Color Scheme</td>
                        <td>Blue & Purple</td>
                        <td>Indigo & Green</td>
                    </tr>
                    <tr>
                        <td>UI Elements</td>
                        <td>Standard</td>
                        <td>Enhanced with subtle animations</td>
                    </tr>
                    <tr>
                        <td>Hero Section</td>
                        <td>Gradient background</td>
                        <td>Angled overlay with wave divider</td>
                    </tr>
                    <tr>
                        <td>Navigation</td>
                        <td>Static navbar</td>
                        <td>Fixed-top with scrolling effect</td>
                    </tr>
                    <tr>
                        <td>Features Section</td>
                        <td>Card-based layout</td>
                        <td>Cards with colored icons</td>
                    </tr>
                    <tr>
                        <td>Authentication Forms</td>
                        <td>Standard forms</td>
                        <td>Enhanced forms with animations</td>
                    </tr>
                    <tr>
                        <td>Mobile Responsiveness</td>
                        <td><i class="fas fa-check-circle check"></i></td>
                        <td><i class="fas fa-check-circle check"></i></td>
                    </tr>
                    <tr>
                        <td>Theme Switching</td>
                        <td><i class="fas fa-check-circle check"></i></td>
                        <td><i class="fas fa-check-circle check"></i></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="text-center mb-5">
            <p>Both designs offer the same functionality with different visual styles.</p>
            <p>You can switch between designs at any time using the theme toggle button.</p>
            <div class="mt-4">
                <a href="index.php" class="btn btn-primary me-3">Use Original Design</a>
                <a href="landing-alternative.php" class="btn btn-outline-primary">Use Alternative Design</a>
            </div>
        </div>
    </div>
    
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/theme-switcher.js"></script>
</body>
</html>
