<?php
// Process login form
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';

    if (empty($username) || empty($password)) {
        $error = "Username and password are required";
    } else {
        if (authenticateUser($username, $password)) {
            // Redirect to dashboard
            header('Location: ' . BASE_URL . '?page=dashboard');
            exit;
        } else {
            $error = "Invalid username or password";
        }
    }
}
?>

<div class="back-to-home">
    <a href="<?php echo BASE_URL; ?>"><i class="fas fa-arrow-left"></i> Back to Home</a>
</div>

<div class="auth-card">
    <div class="card-header">
        <h4 class="mb-0">Welcome Back</h4>
    </div>
    <div class="card-body">
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>

        <form method="post">
            <div class="mb-4">
                <label for="username" class="form-label">Username or Email</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                    <input type="text" class="form-control" id="username" name="username" placeholder="Enter your username or email" required>
                </div>
            </div>

            <div class="mb-4">
                <label for="password" class="form-label">Password</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                    <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" required>
                </div>
            </div>

            <div class="d-grid gap-2 mb-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i> Login
                </button>
            </div>
        </form>

        <div class="text-center">
            <p class="mb-3">
                <a href="<?php echo BASE_URL; ?>?page=forgot-password"><i class="fas fa-key me-1"></i> Forgot Password?</a>
            </p>

            <?php if (getSetting('allow_staff_registration', '1') == '1' || getSetting('allow_contractor_registration', '1') == '1'): ?>
                <p class="mb-0">
                    Don't have an account? <a href="<?php echo BASE_URL; ?>?page=register" class="fw-bold"><i class="fas fa-user-plus me-1"></i> Register Now</a>
                </p>
            <?php endif; ?>
        </div>
    </div>
</div>
