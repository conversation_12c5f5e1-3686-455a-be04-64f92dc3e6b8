<?php
// Include session configuration before starting the session
require_once 'config/session.php';
session_start();

// Include other configuration files
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

// Define the base URL
define('BASE_URL', getBaseUrl());

// Get the current page from the URL
$page = isset($_GET['page']) ? $_GET['page'] : (isLoggedIn() ? 'dashboard' : 'landing');

// Handle redirects before any output
if ($page === 'timesheet' && isLoggedIn()) {
    $timesheetMode = getTimesheetMode();
    header('Location: ' . BASE_URL . '?page=timesheet-' . $timesheetMode);
    exit;
}

// Handle logout before any output
if ($page === 'logout') {
    logoutUser();
    header('Location: ' . BASE_URL . '?page=login');
    exit;
}

// Handle superadmin login-as functionality
if ($page === 'superadmin-login-as' && isSuperAdmin()) {
    $companyId = isset($_GET['company_id']) ? (int)$_GET['company_id'] : 0;

    if ($companyId > 0) {
        // Get company admin
        $stmt = $pdo->prepare("
            SELECT * FROM users
            WHERE company_id = ? AND role = 'admin'
            ORDER BY id LIMIT 1
        ");
        $stmt->execute([$companyId]);
        $admin = $stmt->fetch();

        if ($admin) {
            // Store superadmin session for later
            $_SESSION['superadmin_id'] = $_SESSION['user_id'];

            // Set session to company admin
            $_SESSION['user_id'] = $admin['id'];
            $_SESSION['user_name'] = $admin['username'];
            $_SESSION['user_email'] = $admin['email'];
            $_SESSION['user_role'] = $admin['role'];
            $_SESSION['user_full_name'] = $admin['first_name'] . ' ' . $admin['last_name'];
            $_SESSION['company_id'] = $admin['company_id'];

            // Get company name
            $stmt = $pdo->prepare("SELECT name FROM companies WHERE id = ?");
            $stmt->execute([$companyId]);
            $company = $stmt->fetch();

            if ($company) {
                $_SESSION['company_name'] = $company['name'];
            }

            header('Location: ' . BASE_URL . '?page=dashboard');
            exit;
        }
    }

    // If we get here, something went wrong
    header('Location: ' . BASE_URL . '?page=superadmin-companies&error=Invalid+company+ID');
    exit;
}

// Handle return to superadmin
if ($page === 'return-to-superadmin' && isset($_SESSION['superadmin_id'])) {
    // Get superadmin info
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND role = 'superadmin'");
    $stmt->execute([$_SESSION['superadmin_id']]);
    $superadmin = $stmt->fetch();

    if ($superadmin) {
        // Restore superadmin session
        $_SESSION['user_id'] = $superadmin['id'];
        $_SESSION['user_name'] = $superadmin['username'];
        $_SESSION['user_email'] = $superadmin['email'];
        $_SESSION['user_role'] = $superadmin['role'];
        $_SESSION['user_full_name'] = $superadmin['first_name'] . ' ' . $superadmin['last_name'];

        // Remove company context
        unset($_SESSION['company_id']);
        unset($_SESSION['company_name']);
        unset($_SESSION['superadmin_id']);

        header('Location: ' . BASE_URL . '?page=superadmin');
        exit;
    }

    // If we get here, something went wrong
    header('Location: ' . BASE_URL . '?page=dashboard');
    exit;
}

// Check if user is logged in, if not redirect to login page (except for public pages)
if (!isLoggedIn() && $page != 'login' && $page != 'register' && $page != 'forgot-password' && $page != 'landing') {
    header('Location: ' . BASE_URL . '?page=login');
    exit;
}

// Check for dashboard redirect before any output
if ($page === 'dashboard' && isSuperAdmin() && !isset($_SESSION['company_id'])) {
    header('Location: ' . BASE_URL . '?page=superadmin');
    exit;
}

// Include the header
include 'views/partials/header.php';

// Include the appropriate page
switch ($page) {
    case 'landing':
        include 'views/landing.php';
        break;
    case 'login':
        include 'views/auth/login.php';
        break;
    case 'register':
        include 'views/auth/register.php';
        break;
    case 'forgot-password':
        include 'views/auth/forgot-password.php';
        break;
    case 'dashboard':
        include 'views/dashboard.php';
        break;
    // Timesheet case is handled before any output
    case 'timesheet-daily':
        include 'views/timesheet-daily.php';
        break;
    case 'timesheet-weekly':
        include 'views/timesheet-weekly.php';
        break;
    case 'projects':
        include 'views/projects.php';
        break;
    case 'reports':
        include 'views/reports.php';
        break;

    // Admin pages
    case 'admin':
        // Check if user is admin
        if (isAdmin()) {
            include 'views/admin/index.php';
        } else {
            include 'views/errors/403.php';
        }
        break;
    case 'admin-users':
        if (isAdmin()) {
            include 'views/admin/users.php';
        } else {
            include 'views/errors/403.php';
        }
        break;
    case 'admin-add-user':
        if (isAdmin()) {
            include 'views/admin/admin-add-user.php';
        } else {
            include 'views/errors/403.php';
        }
        break;
    case 'admin-edit-user':
        if (isAdmin()) {
            include 'views/admin/admin-edit-user.php';
        } else {
            include 'views/errors/403.php';
        }
        break;
    case 'admin-projects':
        if (isAdmin()) {
            include 'views/admin/admin-projects.php';
        } else {
            include 'views/errors/403.php';
        }
        break;
    case 'admin-edit-project':
        if (isAdmin()) {
            include 'views/admin/admin-edit-project.php';
        } else {
            include 'views/errors/403.php';
        }
        break;
    case 'admin-timesheets':
        if (isAdmin()) {
            include 'views/admin/admin-timesheets.php';
        } else {
            include 'views/errors/403.php';
        }
        break;
    case 'admin-settings':
        if (isAdmin()) {
            include 'views/admin/settings.php';
        } else {
            include 'views/errors/403.php';
        }
        break;

    // Superadmin pages
    case 'superadmin':
        if (isSuperAdmin()) {
            include 'views/superadmin/index.php';
        } else {
            include 'views/errors/403.php';
        }
        break;
    case 'superadmin-companies':
        if (isSuperAdmin()) {
            include 'views/superadmin/companies.php';
        } else {
            include 'views/errors/403.php';
        }
        break;
    case 'superadmin-edit-company':
        if (isSuperAdmin()) {
            include 'views/superadmin/edit-company.php';
        } else {
            include 'views/errors/403.php';
        }
        break;
    case 'superadmin-users':
        if (isSuperAdmin()) {
            include 'views/superadmin/users.php';
        } else {
            include 'views/errors/403.php';
        }
        break;
    case 'superadmin-subscriptions':
        if (isSuperAdmin()) {
            include 'views/superadmin/subscriptions.php';
        } else {
            include 'views/errors/403.php';
        }
        break;
    case 'superadmin-settings':
        if (isSuperAdmin()) {
            include 'views/superadmin/settings.php';
        } else {
            include 'views/errors/403.php';
        }
        break;

    case 'profile':
        include 'views/profile.php';
        break;
    // Logout case is handled before any output
    default:
        include 'views/errors/404.php';
        break;
}

// Include the footer
include 'views/partials/footer.php';
?>
