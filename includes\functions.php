<?php
/**
 * Helper functions for the TimeTracker application
 */

/**
 * Get the base URL of the application
 *
 * @return string The base URL
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script = dirname($_SERVER['SCRIPT_NAME']);

    // Remove trailing slash if it exists
    if ($script !== '/' && substr($script, -1) === '/') {
        $script = substr($script, 0, -1);
    }

    return $protocol . '://' . $host . $script;
}

/**
 * Sanitize user input
 *
 * @param string $input The input to sanitize
 * @return string The sanitized input
 */
function sanitize($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Format a date according to the application's date format
 *
 * @param string $date The date to format
 * @param string $format The format to use (default: Y-m-d)
 * @return string The formatted date
 */
function formatDate($date, $format = 'Y-m-d') {
    return date($format, strtotime($date));
}

/**
 * Format a time according to the application's time format
 *
 * @param string $time The time to format
 * @param string $format The format to use (default: H:i)
 * @return string The formatted time
 */
function formatTime($time, $format = 'H:i') {
    return date($format, strtotime($time));
}

/**
 * Format a decimal number as hours and minutes
 *
 * @param float $hours The number of hours
 * @return string The formatted hours and minutes
 */
function formatHours($hours) {
    $h = floor($hours);
    $m = round(($hours - $h) * 60);

    return $h . 'h ' . ($m > 0 ? $m . 'm' : '');
}

/**
 * Get the current timesheet mode from settings
 *
 * @return string The timesheet mode ('daily' or 'weekly')
 */
function getTimesheetMode() {
    // If we're in a company context, use company settings
    if (getCurrentCompanyId()) {
        return getCompanySetting('timesheet_mode', 'daily');
    }

    // Otherwise use global setting or default
    return defined('SETTING_TIMESHEET_MODE') ? SETTING_TIMESHEET_MODE : 'daily';
}

/**
 * Check if a date is a weekend
 *
 * @param string $date The date to check
 * @return bool True if the date is a weekend, false otherwise
 */
function isWeekend($date) {
    // If we're in a company context, use company settings
    if (getCurrentCompanyId()) {
        $weekendDaysStr = getCompanySetting('weekend_days', '0,6');
        $weekendDays = explode(',', $weekendDaysStr);
    } else {
        $weekendDays = defined('SETTING_WEEKEND_DAYS') ? explode(',', SETTING_WEEKEND_DAYS) : [0, 6];
    }

    $dayOfWeek = date('w', strtotime($date));

    return in_array($dayOfWeek, $weekendDays);
}

/**
 * Get the start and end dates of a week
 *
 * @param string $date A date in the week
 * @return array An array with the start and end dates of the week
 */
function getWeekDates($date) {
    $date = new DateTime($date);
    $weekDay = $date->format('w');

    // Adjust to get Monday as the first day of the week
    $monday = clone $date;
    $monday->modify('-' . (($weekDay ?: 7) - 1) . ' days');

    $sunday = clone $monday;
    $sunday->modify('+6 days');

    return [
        'start' => $monday->format('Y-m-d'),
        'end' => $sunday->format('Y-m-d')
    ];
}

/**
 * Get all dates in a week
 *
 * @param string $date A date in the week
 * @return array An array with all dates in the week
 */
function getWeekDatesArray($date) {
    $weekDates = getWeekDates($date);
    $dates = [];

    $current = new DateTime($weekDates['start']);
    $end = new DateTime($weekDates['end']);

    while ($current <= $end) {
        $dates[] = $current->format('Y-m-d');
        $current->modify('+1 day');
    }

    return $dates;
}

/**
 * Get user by ID
 *
 * @param int $userId The user ID
 * @return array|false The user data or false if not found
 */
function getUserById($userId) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$userId]);

    return $stmt->fetch();
}

/**
 * Get project by ID
 *
 * @param int $projectId The project ID
 * @return array|false The project data or false if not found
 */
function getProjectById($projectId) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM projects WHERE id = ?");
    $stmt->execute([$projectId]);

    return $stmt->fetch();
}

/**
 * Get all active projects for the current company
 *
 * @param int|null $companyId The company ID (defaults to current company)
 * @return array The active projects
 */
function getActiveProjects($companyId = null) {
    global $pdo;

    if ($companyId === null) {
        $companyId = getCurrentCompanyId();
    }

    if (!$companyId) {
        return [];
    }

    $stmt = $pdo->prepare("SELECT * FROM projects WHERE status = 'active' AND company_id = ? ORDER BY name");
    $stmt->execute([$companyId]);

    return $stmt->fetchAll();
}

/**
 * Get global setting value
 *
 * @param string $key The setting key
 * @param mixed $default The default value if the setting is not found
 * @return mixed The setting value or the default value
 */
function getSetting($key, $default = null) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
    $stmt->execute([$key]);

    $result = $stmt->fetch();

    return $result ? $result['setting_value'] : $default;
}

/**
 * Update global setting value
 *
 * @param string $key The setting key
 * @param mixed $value The setting value
 * @return bool True if the setting was updated, false otherwise
 */
function updateSetting($key, $value) {
    global $pdo;

    $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value)
                          VALUES (?, ?)
                          ON DUPLICATE KEY UPDATE setting_value = ?");

    return $stmt->execute([$key, $value, $value]);
}

/**
 * Get company setting value
 *
 * @param string $key The setting key
 * @param mixed $default The default value if the setting is not found
 * @param int|null $companyId The company ID (defaults to current company)
 * @return mixed The setting value or the default value
 */
function getCompanySetting($key, $default = null, $companyId = null) {
    global $pdo;

    if ($companyId === null) {
        $companyId = getCurrentCompanyId();
    }

    if (!$companyId) {
        return $default;
    }

    $stmt = $pdo->prepare("SELECT setting_value FROM company_settings WHERE company_id = ? AND setting_key = ?");
    $stmt->execute([$companyId, $key]);

    $result = $stmt->fetch();

    return $result ? $result['setting_value'] : $default;
}

/**
 * Update company setting value
 *
 * @param string $key The setting key
 * @param mixed $value The setting value
 * @param int|null $companyId The company ID (defaults to current company)
 * @return bool True if the setting was updated, false otherwise
 */
function updateCompanySetting($key, $value, $companyId = null) {
    global $pdo;

    if ($companyId === null) {
        $companyId = getCurrentCompanyId();
    }

    if (!$companyId) {
        return false;
    }

    $stmt = $pdo->prepare("INSERT INTO company_settings (company_id, setting_key, setting_value)
                          VALUES (?, ?, ?)
                          ON DUPLICATE KEY UPDATE setting_value = ?");

    return $stmt->execute([$companyId, $key, $value, $value]);
}

/**
 * Assign a user to a project
 *
 * @param int $userId The user ID
 * @param int $projectId The project ID
 * @param int|null $companyId The company ID (defaults to current company)
 * @return bool|string True if assignment was successful, error message otherwise
 */
function assignUserToProject($userId, $projectId, $companyId = null) {
    global $pdo;

    try {
        if ($companyId === null) {
            $companyId = getCurrentCompanyId();
        }

        if (!$companyId) {
            return "Company ID is required";
        }

        // Check if user exists and belongs to the company
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ? AND company_id = ?");
        $stmt->execute([$userId, $companyId]);

        if ($stmt->rowCount() === 0) {
            return "User not found or does not belong to the company";
        }

        // Check if project exists and belongs to the company
        $stmt = $pdo->prepare("SELECT * FROM projects WHERE id = ? AND company_id = ?");
        $stmt->execute([$projectId, $companyId]);

        if ($stmt->rowCount() === 0) {
            return "Project not found or does not belong to the company";
        }

        // Check if assignment already exists
        $stmt = $pdo->prepare("SELECT * FROM project_assignments WHERE user_id = ? AND project_id = ?");
        $stmt->execute([$userId, $projectId]);

        if ($stmt->rowCount() > 0) {
            return true; // Already assigned
        }

        // Insert assignment
        $stmt = $pdo->prepare("INSERT INTO project_assignments (user_id, project_id, company_id) VALUES (?, ?, ?)");
        $stmt->execute([$userId, $projectId, $companyId]);

        return true;
    } catch (PDOException $e) {
        return "Error assigning user to project: " . $e->getMessage();
    }
}

/**
 * Remove a user from a project
 *
 * @param int $userId The user ID
 * @param int $projectId The project ID
 * @param int|null $companyId The company ID (defaults to current company)
 * @return bool|string True if removal was successful, error message otherwise
 */
function removeUserFromProject($userId, $projectId, $companyId = null) {
    global $pdo;

    try {
        if ($companyId === null) {
            $companyId = getCurrentCompanyId();
        }

        if (!$companyId) {
            return "Company ID is required";
        }

        $stmt = $pdo->prepare("DELETE FROM project_assignments WHERE user_id = ? AND project_id = ? AND company_id = ?");
        $stmt->execute([$userId, $projectId, $companyId]);

        return true;
    } catch (PDOException $e) {
        return "Error removing user from project: " . $e->getMessage();
    }
}

/**
 * Get all projects assigned to a user
 *
 * @param int $userId The user ID
 * @param int|null $companyId The company ID (defaults to current company)
 * @return array The assigned projects
 */
function getUserProjects($userId, $companyId = null) {
    global $pdo;

    if ($companyId === null) {
        $companyId = getCurrentCompanyId();
    }

    if (!$companyId) {
        return [];
    }

    $stmt = $pdo->prepare("
        SELECT p.*
        FROM projects p
        JOIN project_assignments pa ON p.id = pa.project_id
        WHERE pa.user_id = ? AND p.status = 'active' AND p.company_id = ? AND pa.company_id = ?
        ORDER BY p.name
    ");
    $stmt->execute([$userId, $companyId, $companyId]);

    return $stmt->fetchAll();
}

/**
 * Get all users assigned to a project
 *
 * @param int $projectId The project ID
 * @param int|null $companyId The company ID (defaults to current company)
 * @return array The assigned users
 */
function getProjectUsers($projectId, $companyId = null) {
    global $pdo;

    if ($companyId === null) {
        $companyId = getCurrentCompanyId();
    }

    if (!$companyId) {
        return [];
    }

    $stmt = $pdo->prepare("
        SELECT u.*
        FROM users u
        JOIN project_assignments pa ON u.id = pa.user_id
        WHERE pa.project_id = ? AND u.status = 'active' AND u.company_id = ? AND pa.company_id = ?
        ORDER BY u.first_name, u.last_name
    ");
    $stmt->execute([$projectId, $companyId, $companyId]);

    return $stmt->fetchAll();
}

/**
 * Check if a user is assigned to a project
 *
 * @param int $userId The user ID
 * @param int $projectId The project ID
 * @param int|null $companyId The company ID (defaults to current company)
 * @return bool True if the user is assigned to the project, false otherwise
 */
function isUserAssignedToProject($userId, $projectId, $companyId = null) {
    global $pdo;

    if ($companyId === null) {
        $companyId = getCurrentCompanyId();
    }

    if (!$companyId) {
        return false;
    }

    $stmt = $pdo->prepare("SELECT * FROM project_assignments WHERE user_id = ? AND project_id = ? AND company_id = ?");
    $stmt->execute([$userId, $projectId, $companyId]);

    return $stmt->rowCount() > 0;
}

/**
 * Handle newsletter subscriptions
 * 
 * @param string $email The email address to subscribe
 * @return bool|string True on success, error message on failure
 */
function subscribeToNewsletter($email) {
    global $pdo;
    
    // Validate email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return "Invalid email address";
    }
    
    try {
        // Check if email already exists
        $stmt = $pdo->prepare("SELECT id FROM newsletter_subscribers WHERE email = ?");
        $stmt->execute([$email]);
        
        if ($stmt->rowCount() > 0) {
            return "Email is already subscribed";
        }
        
        // Add new subscriber
        $stmt = $pdo->prepare("INSERT INTO newsletter_subscribers (email, subscribed_at) VALUES (?, NOW())");
        
        if ($stmt->execute([$email])) {
            // Send confirmation email
            $subject = "Newsletter Subscription Confirmation";
            $appName = getSetting('app_name', 'TimeTracker Pro');
            $message = "Thank you for subscribing to the $appName newsletter. You'll now receive updates and tips about time tracking and productivity.";
            $headers = "From: " . getSetting('admin_email', '<EMAIL>');
            
            mail($email, $subject, $message, $headers);
            
            // Log the subscription
            logActivity("New newsletter subscription: $email");
            
            return true;
        } else {
            error_log("Failed to add newsletter subscriber: " . print_r($stmt->errorInfo(), true));
            return "Failed to add subscriber";
        }
    } catch (PDOException $e) {
        error_log("Error in subscribeToNewsletter: " . $e->getMessage());
        return "An error occurred";
    }
}

/**
 * Process newsletter subscription form
 */
if (isset($_POST['action']) && $_POST['action'] === 'subscribe_newsletter') {
    $email = isset($_POST['email']) ? trim($_POST['email']) : '';
    $result = subscribeToNewsletter($email);
    
    // If AJAX request
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        
        if ($result === true) {
            echo json_encode(['success' => true, 'message' => 'Successfully subscribed to the newsletter!']);
        } else {
            echo json_encode(['success' => false, 'message' => $result]);
        }
        exit;
    } else {
        // For non-AJAX forms
        if ($result === true) {
            $_SESSION['success_message'] = 'Successfully subscribed to the newsletter!';
        } else {
            $_SESSION['error_message'] = $result;
        }
        
        // Redirect back to the referring page
        header('Location: ' . $_SERVER['HTTP_REFERER']);
        exit;
    }
}
?>
