<?php
// Get app name from settings
$appName = getSetting('app_name', 'TimeTracker Pro');

// Get subscription plans from database
$plans = getSubscriptionPlans(true);
?>

<!-- Hero Section -->
<section class="hero-section-alt py-5" style="min-height: 60vh;">
    <div class="container">
        <div class="row justify-content-center align-items-center" style="min-height: 50vh;">
            <div class="col-lg-8 text-center hero-text-alt">
                <h1 class="hero-title">Simple, Transparent Pricing</h1>
                <p class="hero-subtitle">Choose the perfect plan for your team. Start free, upgrade when you're ready.</p>
                <div class="mt-4">
                    <div class="pricing-toggle d-inline-flex bg-white rounded-pill p-1 shadow-sm">
                        <button class="btn btn-sm pricing-toggle-btn active" data-period="monthly">Monthly</button>
                        <button class="btn btn-sm pricing-toggle-btn" data-period="yearly">
                            Yearly <span class="badge bg-success ms-1">Save 20%</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Pricing Plans Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <?php foreach ($plans as $index => $plan): ?>
                <?php 
                $features = formatPlanFeatures($plan['features']);
                $isPopular = $plan['name'] === 'Professional';
                $monthlyPrice = $plan['price'];
                $yearlyPrice = $monthlyPrice * 12 * 0.8; // 20% discount for yearly
                ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="pricing-card h-100 <?php echo $isPopular ? 'pricing-card-popular' : ''; ?>">
                        <?php if ($isPopular): ?>
                            <div class="pricing-badge">Most Popular</div>
                        <?php endif; ?>
                        
                        <div class="pricing-header text-center">
                            <h3 class="pricing-title"><?php echo htmlspecialchars($plan['name']); ?></h3>
                            <p class="pricing-description text-muted"><?php echo htmlspecialchars($plan['description']); ?></p>
                            
                            <div class="pricing-price">
                                <span class="price-currency">$</span>
                                <span class="price-amount monthly-price"><?php echo number_format($monthlyPrice, 0); ?></span>
                                <span class="price-amount yearly-price d-none"><?php echo number_format($yearlyPrice / 12, 0); ?></span>
                                <span class="price-period">/month</span>
                            </div>
                            
                            <div class="yearly-savings d-none text-success small">
                                Save $<?php echo number_format(($monthlyPrice * 12) - $yearlyPrice, 0); ?> per year
                            </div>
                        </div>

                        <div class="pricing-features">
                            <ul class="list-unstyled">
                                <?php if ($plan['max_users'] > 0): ?>
                                    <li><i class="fas fa-check text-success me-2"></i>Up to <?php echo $plan['max_users']; ?> users</li>
                                <?php else: ?>
                                    <li><i class="fas fa-check text-success me-2"></i>Unlimited users</li>
                                <?php endif; ?>
                                
                                <?php if ($plan['max_projects'] > 0): ?>
                                    <li><i class="fas fa-check text-success me-2"></i>Up to <?php echo $plan['max_projects']; ?> projects</li>
                                <?php else: ?>
                                    <li><i class="fas fa-check text-success me-2"></i>Unlimited projects</li>
                                <?php endif; ?>
                                
                                <?php foreach ($features as $feature): ?>
                                    <li><i class="fas fa-check text-success me-2"></i><?php echo htmlspecialchars($feature); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>

                        <div class="pricing-footer mt-auto">
                            <?php if ($plan['price'] == 0): ?>
                                <a href="<?php echo BASE_URL; ?>?page=register" class="btn btn-outline-primary btn-lg w-100">
                                    Start Free
                                </a>
                            <?php else: ?>
                                <a href="<?php echo BASE_URL; ?>?page=register" class="btn <?php echo $isPopular ? 'btn-primary' : 'btn-outline-primary'; ?> btn-lg w-100">
                                    Start Free Trial
                                </a>
                            <?php endif; ?>
                            <small class="text-muted d-block text-center mt-2">No credit card required</small>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Features Comparison Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="h1 fw-bold mb-3">Compare All Features</h2>
                <p class="lead text-muted">See exactly what's included in each plan</p>
            </div>
        </div>

        <div class="table-responsive">
            <table class="table table-bordered comparison-table">
                <thead class="table-light">
                    <tr>
                        <th scope="col" class="feature-column">Features</th>
                        <?php foreach ($plans as $plan): ?>
                            <th scope="col" class="text-center plan-column">
                                <div class="fw-bold"><?php echo htmlspecialchars($plan['name']); ?></div>
                                <div class="text-muted small">$<?php echo number_format($plan['price'], 0); ?>/month</div>
                            </th>
                        <?php endforeach; ?>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="fw-semibold">Users</td>
                        <?php foreach ($plans as $plan): ?>
                            <td class="text-center">
                                <?php echo $plan['max_users'] > 0 ? $plan['max_users'] : 'Unlimited'; ?>
                            </td>
                        <?php endforeach; ?>
                    </tr>
                    <tr>
                        <td class="fw-semibold">Projects</td>
                        <?php foreach ($plans as $plan): ?>
                            <td class="text-center">
                                <?php echo $plan['max_projects'] > 0 ? $plan['max_projects'] : 'Unlimited'; ?>
                            </td>
                        <?php endforeach; ?>
                    </tr>
                    
                    <?php
                    // Get all unique features
                    $allFeatures = [];
                    foreach ($plans as $plan) {
                        $planFeatures = formatPlanFeatures($plan['features']);
                        foreach ($planFeatures as $feature) {
                            if (!in_array($feature, $allFeatures)) {
                                $allFeatures[] = $feature;
                            }
                        }
                    }
                    ?>
                    
                    <?php foreach ($allFeatures as $feature): ?>
                        <tr>
                            <td class="fw-semibold"><?php echo htmlspecialchars($feature); ?></td>
                            <?php foreach ($plans as $plan): ?>
                                <?php 
                                $planFeatures = formatPlanFeatures($plan['features']);
                                $hasFeature = in_array($feature, $planFeatures);
                                
                                // Check for "All X features" pattern
                                if (!$hasFeature) {
                                    foreach ($planFeatures as $planFeature) {
                                        if (preg_match('/^All\s+(\w+)\s+features$/i', $planFeature, $matches)) {
                                            $basePlan = $matches[1];
                                            // Find the base plan and check if it has this feature
                                            foreach ($plans as $checkPlan) {
                                                if (strtolower($checkPlan['name']) === strtolower($basePlan)) {
                                                    $basePlanFeatures = formatPlanFeatures($checkPlan['features']);
                                                    if (in_array($feature, $basePlanFeatures)) {
                                                        $hasFeature = true;
                                                        break 2;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                ?>
                                <td class="text-center">
                                    <?php if ($hasFeature): ?>
                                        <i class="fas fa-check text-success"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times text-muted"></i>
                                    <?php endif; ?>
                                </td>
                            <?php endforeach; ?>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="h1 fw-bold mb-3">Frequently Asked Questions</h2>
                <p class="lead text-muted">Everything you need to know about our pricing</p>
            </div>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="accordion" id="pricingFAQ">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                Can I change plans at any time?
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#pricingFAQ">
                            <div class="accordion-body">
                                Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately, and we'll prorate any billing differences.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                Is there a free trial?
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#pricingFAQ">
                            <div class="accordion-body">
                                Yes! All paid plans come with a 14-day free trial. No credit card required to start. The Free plan is available indefinitely.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                What payment methods do you accept?
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#pricingFAQ">
                            <div class="accordion-body">
                                We accept all major credit cards (Visa, MasterCard, American Express) and PayPal. Enterprise customers can also pay by invoice.
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                Can I cancel anytime?
                            </button>
                        </h2>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#pricingFAQ">
                            <div class="accordion-body">
                                Absolutely! You can cancel your subscription at any time. Your account will remain active until the end of your current billing period.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h2 class="h1 fw-bold mb-3">Ready to Get Started?</h2>
                <p class="lead mb-4">Join thousands of teams already using <?php echo $appName; ?> to boost their productivity</p>
                <a href="<?php echo BASE_URL; ?>?page=register" class="btn btn-light btn-lg px-4 py-3 shine-btn">
                    <i class="fas fa-rocket me-2"></i>Start Your Free Trial
                </a>
            </div>
        </div>
    </div>
</section>

<style>
/* Pricing Styles */
.pricing-toggle {
    border: 1px solid #e5e7eb;
}

.pricing-toggle-btn {
    border: none;
    background: transparent;
    color: #6b7280;
    padding: 0.5rem 1.5rem;
    border-radius: 1.5rem;
    transition: all 0.3s ease;
}

.pricing-toggle-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.pricing-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    display: flex;
    flex-direction: column;
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.pricing-card-popular {
    border-color: #667eea;
    transform: scale(1.05);
}

.pricing-card-popular:hover {
    transform: scale(1.05) translateY(-5px);
}

.pricing-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 1.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pricing-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.pricing-description {
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
}

.pricing-price {
    margin-bottom: 1rem;
}

.price-currency {
    font-size: 1.25rem;
    font-weight: 600;
    color: #6b7280;
    vertical-align: top;
}

.price-amount {
    font-size: 3rem;
    font-weight: 800;
    color: #1f2937;
    line-height: 1;
}

.price-period {
    font-size: 1rem;
    color: #6b7280;
    font-weight: 500;
}

.pricing-features {
    flex-grow: 1;
    margin-bottom: 2rem;
}

.pricing-features ul li {
    padding: 0.5rem 0;
    font-size: 0.95rem;
    color: #374151;
}

.comparison-table {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.feature-column {
    background: #f8fafc;
    font-weight: 600;
    width: 30%;
}

.plan-column {
    width: 23.33%;
}

.comparison-table td {
    padding: 1rem;
    vertical-align: middle;
}

.comparison-table th {
    padding: 1.5rem 1rem;
    border-bottom: 2px solid #e5e7eb;
}

@media (max-width: 768px) {
    .pricing-card-popular {
        transform: none;
    }

    .pricing-card-popular:hover {
        transform: translateY(-5px);
    }

    .price-amount {
        font-size: 2.5rem;
    }

    .comparison-table {
        font-size: 0.875rem;
    }

    .comparison-table td,
    .comparison-table th {
        padding: 0.75rem 0.5rem;
    }
}

/* Pricing toggle animation */
.yearly-price,
.yearly-savings {
    transition: all 0.3s ease;
}

.monthly-price {
    transition: all 0.3s ease;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const toggleBtns = document.querySelectorAll('.pricing-toggle-btn');
    const monthlyPrices = document.querySelectorAll('.monthly-price');
    const yearlyPrices = document.querySelectorAll('.yearly-price');
    const yearlySavings = document.querySelectorAll('.yearly-savings');

    toggleBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const period = this.dataset.period;

            // Update active state
            toggleBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            // Toggle prices
            if (period === 'yearly') {
                monthlyPrices.forEach(price => price.classList.add('d-none'));
                yearlyPrices.forEach(price => price.classList.remove('d-none'));
                yearlySavings.forEach(saving => saving.classList.remove('d-none'));
            } else {
                monthlyPrices.forEach(price => price.classList.remove('d-none'));
                yearlyPrices.forEach(price => price.classList.add('d-none'));
                yearlySavings.forEach(saving => saving.classList.add('d-none'));
            }
        });
    });
});
</script>
