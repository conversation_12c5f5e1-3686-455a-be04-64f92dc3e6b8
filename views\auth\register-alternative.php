<?php
// This template contains only the register form content
// The header and footer are included by the parent template

// Check if registration is allowed
$allowStaffRegistration = getSetting('allow_staff_registration', '1') == '1';
$allowContractorRegistration = getSetting('allow_contractor_registration', '1') == '1';

if (!$allowStaffRegistration && !$allowContractorRegistration) {
    echo '<div class="alert alert-danger">Registration is currently disabled.</div>';
    exit;
}
?>

<!-- Alternative Register Section -->
<section class="register-alternative-section py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card register-card shadow-lg border-0 rounded-lg mt-5">
                    <div class="card-header bg-gradient-primary-alt text-white text-center py-4">
                        <h2 class="fw-light mb-0">Create Your Account</h2>
                    </div>
                    <div class="card-body p-5">
                        <?php if (!empty($error)): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        
                        <?php if (!empty($success)): ?>
                            <div class="alert alert-success">
                                <?php echo $success; ?> 
                                <a href="<?php echo BASE_URL; ?>?page=login-alternative">Login here</a>
                            </div>
                        <?php else: ?>
                        
                        <form action="" method="post" class="needs-validation" novalidate>
                            <div class="row mb-3">
                                <div class="col-md-6 mb-3 mb-md-0">
                                    <div class="form-floating">
                                        <input class="form-control rounded-3" id="first_name" type="text" name="first_name" placeholder="First Name" required />
                                        <label for="first_name">First Name</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input class="form-control rounded-3" id="last_name" type="text" name="last_name" placeholder="Last Name" required />
                                        <label for="last_name">Last Name</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <input class="form-control rounded-3" id="username" type="text" name="username" placeholder="Username" required />
                                <label for="username">Username</label>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <input class="form-control rounded-3" id="email" type="email" name="email" placeholder="Email Address" required />
                                <label for="email">Email Address</label>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6 mb-3 mb-md-0">
                                    <div class="form-floating">
                                        <input class="form-control rounded-3" id="password" type="password" name="password" placeholder="Password" required />
                                        <label for="password">Password</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input class="form-control rounded-3" id="confirm_password" type="password" name="confirm_password" placeholder="Confirm Password" required />
                                        <label for="confirm_password">Confirm Password</label>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if ($allowStaffRegistration && $allowContractorRegistration): ?>
                            <div class="mb-4">
                                <label class="form-label">Account Type</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="role" id="role_staff" value="staff" checked>
                                    <label class="form-check-label" for="role_staff">
                                        Staff/Employee
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="role" id="role_contractor" value="contractor">
                                    <label class="form-check-label" for="role_contractor">
                                        Contractor/Freelancer
                                    </label>
                                </div>
                            </div>
                            <?php elseif ($allowStaffRegistration): ?>
                                <input type="hidden" name="role" value="staff">
                            <?php elseif ($allowContractorRegistration): ?>
                                <input type="hidden" name="role" value="contractor">
                            <?php endif; ?>
                            
                            <div class="form-check mb-4">
                                <input class="form-check-input" id="agree_terms" type="checkbox" required />
                                <label class="form-check-label" for="agree_terms">
                                    I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms of Service</a> and <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">Privacy Policy</a>
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button class="btn btn-gradient-primary-alt btn-lg rounded-3 text-white" type="submit">
                                    <i class="fas fa-user-plus me-2"></i>Create Account
                                </button>
                            </div>
                        </form>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer text-center py-4">
                        <div>
                            Already have an account? 
                            <a href="<?php echo BASE_URL; ?>?page=login-alternative">Log in</a>
                        </div>
                        <div class="mt-3">
                            <a href="<?php echo BASE_URL; ?>?page=register" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-palette me-1"></i>Try original design
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Include navigation helper script -->
<script src="<?php echo BASE_URL; ?>assets/js/navigation-helper.js"></script>
    } elseif (strlen($userData['password']) < 8) {
        $error = "Password must be at least 8 characters long";
    } elseif (!filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } elseif (userExists($userData['username'], $userData['email'])) {
        $error = "Username or email already exists";
    } elseif ($userData['role'] === 'staff' && !$allowStaffRegistration) {
        $error = "Staff registration is not allowed";
    } elseif ($userData['role'] === 'contractor' && !$allowContractorRegistration) {
        $error = "Contractor registration is not allowed";
    } else {
        // Create user
        if (registerUser($userData)) {
            $success = "Registration successful! You can now login.";
        } else {
            $error = "An error occurred. Please try again.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - TimeTracker</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/fontawesome.min.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/auth-alternative.css">
    
    <!-- Favicon -->
    <link rel="icon" href="<?php echo BASE_URL; ?>assets/img/favicon.png">
</head>
<body>    <div class="auth-page-alt">
        <div class="back-to-home-alt">
            <a href="<?php echo BASE_URL; ?>landing-alternative.php">
                <i class="fas fa-arrow-left"></i> Back to Home
            </a>
        </div>
        
        <div class="auth-nav-alt">
            <a href="<?php echo BASE_URL; ?>?page=login-alternative">
                <i class="fas fa-sign-in-alt"></i> Login
            </a>
            <a href="<?php echo BASE_URL; ?>design-comparison.php">
                <i class="fas fa-palette"></i> Designs
            </a>
        </div>
        
        <div class="container">
            <div class="auth-card-alt">
                <div class="auth-header-alt">
                    <div class="brand text-center mb-4">
                        <img src="<?php echo BASE_URL; ?>assets/img/logo.png" alt="TimeTracker Logo" height="40">
                    </div>
                    <h3>Create Your Account</h3>
                    <p>Join TimeTracker and manage your time effectively</p>
                </div>
                
                <div class="auth-body-alt">
                    <?php if ($error): ?>
                        <div class="alert-alt alert-danger-alt">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert-alt alert-success-alt">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo $success; ?>
                        </div>
                        <div class="text-center mt-4">
                            <a href="<?php echo BASE_URL; ?>?page=login-alternative" class="btn-alt btn-primary-alt">
                                <i class="fas fa-sign-in-alt me-2"></i> Go to Login
                            </a>
                        </div>
                    <?php else: ?>
                        <form method="post">
                            <?php if ($allowStaffRegistration && $allowContractorRegistration): ?>
                                <div class="role-selection">
                                    <div class="role-option">
                                        <input type="radio" id="roleStaff" name="role" value="staff" checked>
                                        <label for="roleStaff">
                                            <i class="fas fa-user-tie"></i>
                                            <div class="role-title">Staff</div>
                                            <div class="role-desc">Internal employee with regular hours</div>
                                        </label>
                                    </div>
                                    <div class="role-option">
                                        <input type="radio" id="roleContractor" name="role" value="contractor">
                                        <label for="roleContractor">
                                            <i class="fas fa-user-cog"></i>
                                            <div class="role-title">Contractor</div>
                                            <div class="role-desc">External worker with flexible hours</div>
                                        </label>
                                    </div>
                                </div>
                            <?php elseif ($allowStaffRegistration): ?>
                                <input type="hidden" name="role" value="staff">
                            <?php elseif ($allowContractorRegistration): ?>
                                <input type="hidden" name="role" value="contractor">
                            <?php endif; ?>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group-alt">
                                        <label for="first_name">First Name</label>
                                        <div class="input-group-alt">
                                            <input type="text" class="form-control-alt" id="first_name" name="first_name" placeholder="Enter your first name" required>
                                            <span class="input-group-icon">
                                                <i class="fas fa-user"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group-alt">
                                        <label for="last_name">Last Name</label>
                                        <div class="input-group-alt">
                                            <input type="text" class="form-control-alt" id="last_name" name="last_name" placeholder="Enter your last name" required>
                                            <span class="input-group-icon">
                                                <i class="fas fa-user"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group-alt">
                                <label for="username">Username</label>
                                <div class="input-group-alt">
                                    <input type="text" class="form-control-alt" id="username" name="username" placeholder="Choose a username" required>
                                    <span class="input-group-icon">
                                        <i class="fas fa-user-tag"></i>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="form-group-alt">
                                <label for="email">Email Address</label>
                                <div class="input-group-alt">
                                    <input type="email" class="form-control-alt" id="email" name="email" placeholder="Enter your email address" required>
                                    <span class="input-group-icon">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="form-group-alt">
                                <label for="password">Password</label>
                                <div class="input-group-alt">
                                    <input type="password" class="form-control-alt" id="password" name="password" placeholder="Choose a strong password" required>
                                    <span class="input-group-icon">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <span class="password-toggle" id="togglePassword">
                                        <i class="far fa-eye"></i>
                                    </span>
                                </div>
                                <small class="text-muted">Password must be at least 8 characters long</small>
                            </div>
                            
                            <div class="form-group-alt">
                                <label for="confirm_password">Confirm Password</label>
                                <div class="input-group-alt">
                                    <input type="password" class="form-control-alt" id="confirm_password" name="confirm_password" placeholder="Confirm your password" required>
                                    <span class="input-group-icon">
                                        <i class="fas fa-lock-open"></i>
                                    </span>
                                    <span class="password-toggle" id="toggleConfirmPassword">
                                        <i class="far fa-eye"></i>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="form-group-alt">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="termsCheck" required>
                                    <label class="form-check-label" for="termsCheck">
                                        I agree to the <a href="#" class="auth-link">Terms of Service</a> and <a href="#" class="auth-link">Privacy Policy</a>
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn-alt btn-primary-alt btn-block-alt">
                                <i class="fas fa-user-plus me-2"></i> Create Account
                            </button>
                        </form>
                        
                        <div class="auth-divider">
                            <span>or continue with</span>
                        </div>
                        
                        <div class="social-buttons">
                            <button type="button" class="social-btn social-btn-google">
                                <i class="fab fa-google"></i> Google
                            </button>
                            <button type="button" class="social-btn social-btn-facebook">
                                <i class="fab fa-facebook-f"></i> Facebook
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
                  <div class="auth-footer-alt">
                    <p>
                        Already have an account? 
                        <a href="<?php echo BASE_URL; ?>?page=login-alternative" class="auth-link fw-bold">
                            Sign In
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>    <!-- JavaScript Files -->
    <script src="<?php echo BASE_URL; ?>assets/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/theme-switcher.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/navigation-helper.js"></script>
    <script>
        // Password visibility toggle
        document.addEventListener('DOMContentLoaded', function() {
            const togglePassword = document.getElementById('togglePassword');
            const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            
            // Toggle main password visibility
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                // Toggle eye icon
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });
            
            // Toggle confirm password visibility
            toggleConfirmPassword.addEventListener('click', function() {
                const type = confirmPasswordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                confirmPasswordInput.setAttribute('type', type);
                
                // Toggle eye icon
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });
            
            // Password validation
            const passwordField = document.getElementById('password');
            const confirmPasswordField = document.getElementById('confirm_password');
            
            confirmPasswordField.addEventListener('input', function() {
                if (passwordField.value !== confirmPasswordField.value) {
                    confirmPasswordField.setCustomValidity("Passwords do not match");
                } else {
                    confirmPasswordField.setCustomValidity("");
                }
            });
            
            passwordField.addEventListener('input', function() {
                if (confirmPasswordField.value && passwordField.value !== confirmPasswordField.value) {
                    confirmPasswordField.setCustomValidity("Passwords do not match");
                } else {
                    confirmPasswordField.setCustomValidity("");
                }
            });
        });
    </script>
</body>
</html>
