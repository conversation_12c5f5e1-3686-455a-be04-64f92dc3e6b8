<?php
// Get app name from settings
$appName = getSetting('app_name', 'TimeTracker Pro');

// Get testimonials from database
$featuredTestimonials = getTestimonials(true, 6);
$allTestimonials = getTestimonials(false, 12);
?>

<!-- Hero Section -->
<section class="hero-section-alt py-5" style="min-height: 60vh;">
    <div class="container">
        <div class="row justify-content-center align-items-center" style="min-height: 50vh;">
            <div class="col-lg-8 text-center hero-text-alt">
                <h1 class="hero-title">What Our Customers Say</h1>
                <p class="hero-subtitle">Discover how teams worldwide are transforming their productivity with <?php echo $appName; ?></p>
                <div class="mt-4">
                    <div class="d-flex justify-content-center align-items-center gap-3 mb-3">
                        <div class="text-warning">
                            <?php for($i = 0; $i < 5; $i++): ?>
                                <i class="fas fa-star"></i>
                            <?php endfor; ?>
                        </div>
                        <span class="text-white opacity-90 fw-semibold">4.9/5 from 500+ reviews</span>
                    </div>
                    <p class="text-white opacity-80">Join thousands of satisfied customers</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Testimonials Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="h1 fw-bold mb-3">Featured Reviews</h2>
                <p class="lead text-muted">See what industry leaders are saying about our platform</p>
            </div>
        </div>

        <div class="row">
            <?php foreach ($featuredTestimonials as $testimonial): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="testimonial-card h-100">
                    <div class="testimonial-content">
                        <div class="testimonial-rating mb-3">
                            <?php for($i = 0; $i < $testimonial['rating']; $i++): ?>
                                <i class="fas fa-star text-warning"></i>
                            <?php endfor; ?>
                            <?php for($i = $testimonial['rating']; $i < 5; $i++): ?>
                                <i class="far fa-star text-muted"></i>
                            <?php endfor; ?>
                        </div>
                        <blockquote class="testimonial-quote">
                            "<?php echo htmlspecialchars($testimonial['content']); ?>"
                        </blockquote>
                    </div>
                    <div class="testimonial-author">
                        <div class="d-flex align-items-center">
                            <div class="testimonial-avatar">
                                <?php if (!empty($testimonial['avatar'])): ?>
                                    <img src="<?php echo BASE_URL . $testimonial['avatar']; ?>" alt="<?php echo htmlspecialchars($testimonial['name']); ?>" class="rounded-circle">
                                <?php else: ?>
                                    <div class="avatar-placeholder">
                                        <?php echo strtoupper(substr($testimonial['name'], 0, 1)); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="ms-3">
                                <h6 class="mb-0 fw-semibold"><?php echo htmlspecialchars($testimonial['name']); ?></h6>
                                <?php if (!empty($testimonial['position']) || !empty($testimonial['company'])): ?>
                                    <small class="text-muted">
                                        <?php 
                                        $details = [];
                                        if (!empty($testimonial['position'])) $details[] = $testimonial['position'];
                                        if (!empty($testimonial['company'])) $details[] = $testimonial['company'];
                                        echo htmlspecialchars(implode(' at ', $details));
                                        ?>
                                    </small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- All Testimonials Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <h2 class="h1 fw-bold mb-3">More Customer Stories</h2>
                <p class="lead text-muted">Read what our entire community has to say</p>
            </div>
        </div>

        <div class="row">
            <?php foreach ($allTestimonials as $index => $testimonial): ?>
                <?php if (!$testimonial['featured']): // Skip featured testimonials as they're already shown ?>
                <div class="col-lg-6 mb-4">
                    <div class="testimonial-card-simple h-100">
                        <div class="testimonial-rating mb-2">
                            <?php for($i = 0; $i < $testimonial['rating']; $i++): ?>
                                <i class="fas fa-star text-warning"></i>
                            <?php endfor; ?>
                            <?php for($i = $testimonial['rating']; $i < 5; $i++): ?>
                                <i class="far fa-star text-muted"></i>
                            <?php endfor; ?>
                        </div>
                        <blockquote class="testimonial-quote-simple mb-3">
                            "<?php echo htmlspecialchars($testimonial['content']); ?>"
                        </blockquote>
                        <div class="testimonial-author-simple">
                            <strong><?php echo htmlspecialchars($testimonial['name']); ?></strong>
                            <?php if (!empty($testimonial['position']) || !empty($testimonial['company'])): ?>
                                <span class="text-muted">
                                    - 
                                    <?php 
                                    $details = [];
                                    if (!empty($testimonial['position'])) $details[] = $testimonial['position'];
                                    if (!empty($testimonial['company'])) $details[] = $testimonial['company'];
                                    echo htmlspecialchars(implode(' at ', $details));
                                    ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h2 class="h1 fw-bold mb-3">Ready to Join Our Happy Customers?</h2>
                <p class="lead mb-4">Start your free trial today and see why thousands of teams trust <?php echo $appName; ?></p>
                <div class="d-flex flex-column flex-sm-row align-items-center justify-content-center gap-3">
                    <a href="<?php echo BASE_URL; ?>?page=register" class="btn btn-light btn-lg px-4 py-3 shine-btn">
                        <i class="fas fa-user-plus me-2"></i>Start Free Trial
                    </a>
                    <a href="<?php echo BASE_URL; ?>?page=contact" class="btn btn-outline-light btn-lg px-4 py-3">
                        <i class="fas fa-envelope me-2"></i>Contact Sales
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
/* Testimonial Styles */
.testimonial-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.testimonial-quote {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #374151;
    font-style: italic;
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.testimonial-avatar img {
    width: 50px;
    height: 50px;
    object-fit: cover;
}

.avatar-placeholder {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.2rem;
}

.testimonial-card-simple {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
}

.testimonial-card-simple:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.testimonial-quote-simple {
    font-size: 1rem;
    line-height: 1.5;
    color: #374151;
    font-style: italic;
}

.testimonial-rating {
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .testimonial-card {
        padding: 1.5rem;
    }
    
    .testimonial-quote {
        font-size: 1rem;
    }
}
</style>
