<?php
// Get current user
$currentUser = getCurrentUser();

// Get week from query string or use current week
$weekDate = isset($_GET['week']) ? $_GET['week'] : date('Y-m-d');

// Validate date format
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $weekDate)) {
    $weekDate = date('Y-m-d');
}

// Get week dates
$weekDates = getWeekDates($weekDate);
$weekDatesArray = getWeekDatesArray($weekDate);

// Get projects based on user role
if (isAdmin()) {
    // <PERSON><PERSON> can see all active projects
    $projects = getActiveProjects();
} else {
    // Staff and contractors can only see projects they're assigned to
    $projects = getUserProjects($currentUser['id']);
}

// Get timesheet entries for the selected week
$stmt = $pdo->prepare("
    SELECT t.*, p.name as project_name, p.id as project_id
    FROM timesheets t
    JOIN projects p ON t.project_id = p.id
    WHERE t.user_id = ? AND t.date BETWEEN ? AND ?
    ORDER BY t.date ASC, p.name ASC
");
$stmt->execute([$currentUser['id'], $weekDates['start'], $weekDates['end']]);
$timesheets = $stmt->fetchAll();

// Organize timesheets by project and date
$timesheetsByProject = [];
$projectTotals = [];
$dateTotals = [];

foreach ($weekDatesArray as $date) {
    $dateTotals[$date] = 0;
}

foreach ($projects as $project) {
    $projectId = $project['id'];
    $timesheetsByProject[$projectId] = [
        'project_name' => $project['name'],
        'dates' => []
    ];

    foreach ($weekDatesArray as $date) {
        $timesheetsByProject[$projectId]['dates'][$date] = [
            'hours' => 0,
            'timesheet_id' => null,
            'status' => null,
            'description' => null
        ];
    }

    $projectTotals[$projectId] = 0;
}

foreach ($timesheets as $timesheet) {
    $projectId = $timesheet['project_id'];
    $date = $timesheet['date'];

    if (!isset($timesheetsByProject[$projectId])) {
        $timesheetsByProject[$projectId] = [
            'project_name' => $timesheet['project_name'],
            'dates' => []
        ];

        foreach ($weekDatesArray as $d) {
            $timesheetsByProject[$projectId]['dates'][$d] = [
                'hours' => 0,
                'timesheet_id' => null,
                'status' => null,
                'description' => null
            ];
        }

        $projectTotals[$projectId] = 0;
    }

    $timesheetsByProject[$projectId]['dates'][$date] = [
        'hours' => $timesheet['hours'],
        'timesheet_id' => $timesheet['id'],
        'status' => $timesheet['status'],
        'description' => $timesheet['description']
    ];

    $projectTotals[$projectId] += $timesheet['hours'];
    $dateTotals[$date] += $timesheet['hours'];
}

// Calculate total hours for the week
$totalWeekHours = array_sum($dateTotals);

// Process form submission
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if it's a delete request
    if (isset($_POST['delete']) && isset($_POST['timesheet_id'])) {
        $timesheetId = $_POST['timesheet_id'];

        // Check if the timesheet belongs to the current user
        $stmt = $pdo->prepare("SELECT * FROM timesheets WHERE id = ? AND user_id = ?");
        $stmt->execute([$timesheetId, $currentUser['id']]);

        if ($stmt->rowCount() > 0) {
            // Delete the timesheet
            $stmt = $pdo->prepare("DELETE FROM timesheets WHERE id = ?");
            $stmt->execute([$timesheetId]);

            $success = "Time entry deleted successfully";

            // Refresh the page to update the list
            header('Location: ' . BASE_URL . '?page=timesheet-weekly&week=' . $weekDate . '&success=' . urlencode($success));
            exit;
        } else {
            $error = "You don't have permission to delete this time entry";
        }
    } else {
        // Add new timesheet entry
        $projectId = $_POST['project_id'] ?? '';
        $date = $_POST['date'] ?? '';
        $hours = $_POST['hours'] ?? '';
        $description = $_POST['description'] ?? '';

        // Validate form data
        if (empty($projectId) || empty($date) || empty($hours)) {
            $error = "Project, date, and hours are required";
        } elseif (!is_numeric($hours) || $hours <= 0) {
            $error = "Hours must be a positive number";
        } elseif (!in_array($date, $weekDatesArray)) {
            $error = "Selected date is not in the current week";
        } else {
            try {
                // Check if an entry already exists for this project and date
                $stmt = $pdo->prepare("
                    SELECT * FROM timesheets
                    WHERE user_id = ? AND project_id = ? AND date = ?
                ");
                $stmt->execute([$currentUser['id'], $projectId, $date]);

                if ($stmt->rowCount() > 0) {
                    // Update existing entry
                    $existingTimesheet = $stmt->fetch();

                    $stmt = $pdo->prepare("
                        UPDATE timesheets
                        SET hours = ?, description = ?
                        WHERE id = ?
                    ");

                    $stmt->execute([
                        $hours,
                        $description,
                        $existingTimesheet['id']
                    ]);

                    $success = "Time entry updated successfully";
                } else {
                    // Insert new entry
                    $stmt = $pdo->prepare("
                        INSERT INTO timesheets (user_id, project_id, date, hours, description)
                        VALUES (?, ?, ?, ?, ?)
                    ");

                    $stmt->execute([
                        $currentUser['id'],
                        $projectId,
                        $date,
                        $hours,
                        $description
                    ]);

                    $success = "Time entry added successfully";
                }

                // Refresh the page to update the list
                header('Location: ' . BASE_URL . '?page=timesheet-weekly&week=' . $weekDate . '&success=' . urlencode($success));
                exit;
            } catch (PDOException $e) {
                $error = "Error adding time entry: " . $e->getMessage();
            }
        }
    }
}

// Check for success message in query string
if (isset($_GET['success'])) {
    $success = $_GET['success'];
}

// Calculate previous and next week dates
$prevWeek = date('Y-m-d', strtotime($weekDates['start'] . ' -7 days'));
$nextWeek = date('Y-m-d', strtotime($weekDates['start'] . ' +7 days'));
?>

<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="h3">Weekly Timesheet</h1>
        <p class="text-muted">
            Week of <?php echo formatDate($weekDates['start'], 'F j'); ?> -
            <?php echo formatDate($weekDates['end'], 'F j, Y'); ?>
        </p>
    </div>
    <div class="col-md-6 text-md-end">
        <div class="btn-group" role="group">
            <a href="<?php echo BASE_URL; ?>?page=timesheet-weekly&week=<?php echo $prevWeek; ?>" class="btn btn-outline-secondary">
                <i class="fas fa-chevron-left"></i> Previous Week
            </a>
            <a href="<?php echo BASE_URL; ?>?page=timesheet-weekly&week=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-primary">
                Current Week
            </a>
            <a href="<?php echo BASE_URL; ?>?page=timesheet-weekly&week=<?php echo $nextWeek; ?>" class="btn btn-outline-secondary">
                Next Week <i class="fas fa-chevron-right"></i>
            </a>
        </div>
    </div>
</div>

<?php if ($error): ?>
    <div class="alert alert-danger"><?php echo $error; ?></div>
<?php endif; ?>

<?php if ($success): ?>
    <div class="alert alert-success"><?php echo $success; ?></div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Add Time Entry</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <div class="mb-3">
                        <label for="project_id" class="form-label">Project</label>
                        <select class="form-select" id="project_id" name="project_id" required>
                            <option value="">Select Project</option>
                            <?php foreach ($projects as $project): ?>
                                <option value="<?php echo $project['id']; ?>"><?php echo $project['name']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="date" class="form-label">Date</label>
                        <select class="form-select" id="date" name="date" required>
                            <?php foreach ($weekDatesArray as $date): ?>
                                <option value="<?php echo $date; ?>" <?php echo $date === date('Y-m-d') ? 'selected' : ''; ?>>
                                    <?php echo formatDate($date, 'l, F j'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="hours" class="form-label">Hours</label>
                        <input type="number" class="form-control" id="hours" name="hours" step="0.25" min="0.25" max="24" required>
                        <div class="form-text">Enter time in hours (e.g., 1.5 for 1 hour and 30 minutes)</div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Add Time Entry</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Weekly Time Summary</h5>
                <span class="badge bg-primary">Total: <?php echo number_format($totalWeekHours, 2); ?> hours</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>Project</th>
                                <?php foreach ($weekDatesArray as $date): ?>
                                    <th class="text-center <?php echo isWeekend($date) ? 'table-secondary' : ''; ?>">
                                        <?php echo formatDate($date, 'D'); ?><br>
                                        <small><?php echo formatDate($date, 'j'); ?></small>
                                    </th>
                                <?php endforeach; ?>
                                <th class="text-center">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($timesheetsByProject)): ?>
                                <tr>
                                    <td colspan="<?php echo count($weekDatesArray) + 2; ?>" class="text-center">
                                        No time entries found for this week.
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($timesheetsByProject as $projectId => $project): ?>
                                    <?php if ($projectTotals[$projectId] > 0): ?>
                                        <tr>
                                            <td><?php echo $project['project_name']; ?></td>

                                            <?php foreach ($weekDatesArray as $date): ?>
                                                <td class="text-center <?php echo isWeekend($date) ? 'table-secondary' : ''; ?>">
                                                    <?php if ($project['dates'][$date]['hours'] > 0): ?>
                                                        <div class="position-relative">
                                                            <?php echo number_format($project['dates'][$date]['hours'], 2); ?>

                                                            <?php if ($project['dates'][$date]['status'] === 'pending'): ?>
                                                                <form method="post" class="position-absolute top-0 end-0" onsubmit="return confirm('Are you sure you want to delete this time entry?');">
                                                                    <input type="hidden" name="timesheet_id" value="<?php echo $project['dates'][$date]['timesheet_id']; ?>">
                                                                    <input type="hidden" name="delete" value="1">
                                                                    <button type="submit" class="btn btn-sm btn-link text-danger p-0" title="Delete">
                                                                        <i class="fas fa-times"></i>
                                                                    </button>
                                                                </form>
                                                            <?php elseif ($project['dates'][$date]['status'] === 'approved'): ?>
                                                                <span class="position-absolute top-0 end-0 text-success" title="Approved">
                                                                    <i class="fas fa-check"></i>
                                                                </span>
                                                            <?php elseif ($project['dates'][$date]['status'] === 'rejected'): ?>
                                                                <span class="position-absolute top-0 end-0 text-danger" title="Rejected">
                                                                    <i class="fas fa-ban"></i>
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php else: ?>
                                                        -
                                                    <?php endif; ?>
                                                </td>
                                            <?php endforeach; ?>

                                            <td class="text-center fw-bold">
                                                <?php echo number_format($projectTotals[$projectId], 2); ?>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                        <tfoot>
                            <tr class="table-primary">
                                <th>Daily Total</th>
                                <?php foreach ($weekDatesArray as $date): ?>
                                    <th class="text-center">
                                        <?php echo number_format($dateTotals[$date], 2); ?>
                                    </th>
                                <?php endforeach; ?>
                                <th class="text-center">
                                    <?php echo number_format($totalWeekHours, 2); ?>
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
