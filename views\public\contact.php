<?php
// Get app name from settings
$appName = getSetting('app_name', 'TimeTracker Pro');

// Process contact form
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $subject = $_POST['subject'] ?? '';
    $message = $_POST['message'] ?? '';
    
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error = "All fields are required";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } else {
        // In a real application, you would send the email here
        // For now, we'll just show a success message
        $success = "Thank you for your message! We'll get back to you within 24 hours.";
        
        // Clear form data on success
        $name = $email = $subject = $message = '';
    }
}
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Header Section -->
            <div class="text-center mb-5">
                <h1 class="display-4 fw-bold mb-4">Contact Us</h1>
                <p class="lead text-muted">We'd love to hear from you. Get in touch with our team.</p>
            </div>

            <div class="row">
                <!-- Contact Form -->
                <div class="col-lg-8 mb-5">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-5">
                            <h2 class="h3 mb-4">Send us a Message</h2>
                            
                            <?php if ($error): ?>
                                <div class="alert alert-danger"><?php echo $error; ?></div>
                            <?php endif; ?>
                            
                            <?php if ($success): ?>
                                <div class="alert alert-success"><?php echo $success; ?></div>
                            <?php endif; ?>
                            
                            <form method="post">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Full Name</label>
                                        <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="subject" class="form-label">Subject</label>
                                    <select class="form-select" id="subject" name="subject" required>
                                        <option value="">Choose a subject...</option>
                                        <option value="General Inquiry" <?php echo ($subject ?? '') === 'General Inquiry' ? 'selected' : ''; ?>>General Inquiry</option>
                                        <option value="Technical Support" <?php echo ($subject ?? '') === 'Technical Support' ? 'selected' : ''; ?>>Technical Support</option>
                                        <option value="Billing Question" <?php echo ($subject ?? '') === 'Billing Question' ? 'selected' : ''; ?>>Billing Question</option>
                                        <option value="Feature Request" <?php echo ($subject ?? '') === 'Feature Request' ? 'selected' : ''; ?>>Feature Request</option>
                                        <option value="Partnership" <?php echo ($subject ?? '') === 'Partnership' ? 'selected' : ''; ?>>Partnership</option>
                                        <option value="Other" <?php echo ($subject ?? '') === 'Other' ? 'selected' : ''; ?>>Other</option>
                                    </select>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="message" class="form-label">Message</label>
                                    <textarea class="form-control" id="message" name="message" rows="6" placeholder="Tell us how we can help you..." required><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Send Message
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="col-lg-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body p-4">
                            <h3 class="h4 mb-4">Get in Touch</h3>
                            
                            <div class="contact-item mb-4">
                                <div class="d-flex align-items-start">
                                    <div class="bg-primary text-white rounded-circle p-2 me-3 flex-shrink-0">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div>
                                        <h4 class="h6 mb-1">Email Support</h4>
                                        <p class="text-muted mb-0"><EMAIL></p>
                                        <small class="text-muted">Response within 24 hours</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="contact-item mb-4">
                                <div class="d-flex align-items-start">
                                    <div class="bg-success text-white rounded-circle p-2 me-3 flex-shrink-0">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    <div>
                                        <h4 class="h6 mb-1">Phone Support</h4>
                                        <p class="text-muted mb-0">+****************</p>
                                        <small class="text-muted">Mon-Fri, 9AM-6PM EST</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="contact-item mb-4">
                                <div class="d-flex align-items-start">
                                    <div class="bg-info text-white rounded-circle p-2 me-3 flex-shrink-0">
                                        <i class="fas fa-comments"></i>
                                    </div>
                                    <div>
                                        <h4 class="h6 mb-1">Live Chat</h4>
                                        <p class="text-muted mb-0">Available in-app</p>
                                        <small class="text-muted">Mon-Fri, 9AM-6PM EST</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="contact-item">
                                <div class="d-flex align-items-start">
                                    <div class="bg-warning text-white rounded-circle p-2 me-3 flex-shrink-0">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    <div>
                                        <h4 class="h6 mb-1">Office</h4>
                                        <p class="text-muted mb-0">
                                            123 Business Ave<br>
                                            Suite 100<br>
                                            New York, NY 10001
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- FAQ Section -->
            <div class="row mt-5">
                <div class="col-lg-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-5">
                            <h2 class="h3 mb-4">Frequently Asked Questions</h2>
                            
                            <div class="accordion" id="faqAccordion">
                                <div class="accordion-item">
                                    <h3 class="accordion-header" id="faq1">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                            How quickly can I get started with <?php echo $appName; ?>?
                                        </button>
                                    </h3>
                                    <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            You can get started immediately! Simply sign up for a free account, and you'll be tracking time within minutes. No credit card required for the trial.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h3 class="accordion-header" id="faq2">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                            Do you offer customer support?
                                        </button>
                                    </h3>
                                    <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            Yes! We offer email support for all users, with priority support for paid plans. Our team typically responds within 24 hours, often much faster.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h3 class="accordion-header" id="faq3">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                            Can I import data from other time tracking tools?
                                        </button>
                                    </h3>
                                    <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            Yes, we support data import from most popular time tracking tools. Contact our support team for assistance with migration.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
