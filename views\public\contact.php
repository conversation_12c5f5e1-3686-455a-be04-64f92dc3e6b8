<?php
// Get app name from settings
$appName = getSetting('app_name', 'TimeTracker Pro');

// Process contact form
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $subject = $_POST['subject'] ?? '';
    $message = $_POST['message'] ?? '';

    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error = "All fields are required";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } else {
        // In a real application, you would send the email here
        // For now, we'll just show a success message
        $success = "Thank you for your message! We'll get back to you within 24 hours.";

        // Clear form data on success
        $name = $email = $subject = $message = '';
    }
}
?>

<!-- Hero Section -->
<section class="hero-section-alt py-5" style="min-height: 60vh;">
    <div class="container">
        <div class="row justify-content-center align-items-center" style="min-height: 50vh;">
            <div class="col-lg-8 text-center hero-text-alt">
                <h1 class="hero-title">Get in Touch</h1>
                <p class="hero-subtitle">We'd love to hear from you. Our team is here to help with any questions.</p>
                <div class="mt-4">
                    <div class="d-flex justify-content-center align-items-center gap-4 mb-3 flex-wrap">
                        <div class="contact-stat text-center">
                            <div class="text-white fw-bold h4 mb-0">< 2hrs</div>
                            <small class="text-white opacity-80">Average Response</small>
                        </div>
                        <div class="contact-stat text-center">
                            <div class="text-white fw-bold h4 mb-0">24/7</div>
                            <small class="text-white opacity-80">Support Available</small>
                        </div>
                        <div class="contact-stat text-center">
                            <div class="text-white fw-bold h4 mb-0">99%</div>
                            <small class="text-white opacity-80">Satisfaction Rate</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Main Content -->
<main class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="row">
                    <!-- Contact Form -->
                    <div class="col-lg-8 mb-5">
                        <div class="contact-form-card">
                            <div class="contact-form-header">
                                <h2 class="h3 mb-2">Send us a Message</h2>
                                <p class="text-muted mb-4">Fill out the form below and we'll get back to you within 24 hours.</p>
                            </div>

                            <?php if ($error): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>

                            <?php if ($success): ?>
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>

                            <form method="post" id="contactForm" novalidate>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">Full Name *</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                                            <div class="invalid-feedback">Please provide your full name.</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address *</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                            <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                            <div class="invalid-feedback">Please provide a valid email address.</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="subject" class="form-label">Subject *</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-tag"></i></span>
                                        <select class="form-select" id="subject" name="subject" required>
                                            <option value="">Choose a subject...</option>
                                            <option value="General Inquiry" <?php echo ($subject ?? '') === 'General Inquiry' ? 'selected' : ''; ?>>General Inquiry</option>
                                            <option value="Sales Question" <?php echo ($subject ?? '') === 'Sales Question' ? 'selected' : ''; ?>>Sales Question</option>
                                            <option value="Technical Support" <?php echo ($subject ?? '') === 'Technical Support' ? 'selected' : ''; ?>>Technical Support</option>
                                            <option value="Feature Request" <?php echo ($subject ?? '') === 'Feature Request' ? 'selected' : ''; ?>>Feature Request</option>
                                            <option value="Partnership" <?php echo ($subject ?? '') === 'Partnership' ? 'selected' : ''; ?>>Partnership</option>
                                            <option value="Other" <?php echo ($subject ?? '') === 'Other' ? 'selected' : ''; ?>>Other</option>
                                        </select>
                                        <div class="invalid-feedback">Please select a subject.</div>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="message" class="form-label">Message *</label>
                                    <textarea class="form-control" id="message" name="message" rows="6" placeholder="Tell us how we can help you..." required><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                                    <div class="invalid-feedback">Please provide your message (minimum 10 characters).</div>
                                    <div class="form-text">Minimum 10 characters required.</div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg contact-submit-btn">
                                        <span class="btn-text">
                                            <i class="fas fa-paper-plane me-2"></i>Send Message
                                        </span>
                                        <span class="btn-loading d-none">
                                            <i class="fas fa-spinner fa-spin me-2"></i>Sending...
                                        </span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                    <!-- Contact Information -->
                    <div class="col-lg-4">
                        <div class="feature-card-alt h-100">
                            <h3 class="h4 mb-4">Get in Touch</h3>
                            
                            <div class="contact-item mb-4">
                                <div class="d-flex align-items-start">
                                    <div class="bg-primary text-white rounded-circle p-2 me-3 flex-shrink-0">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div>
                                        <h4 class="h6 mb-1">Email Support</h4>
                                        <p class="text-muted mb-0"><EMAIL></p>
                                        <small class="text-muted">Response within 24 hours</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="contact-item mb-4">
                                <div class="d-flex align-items-start">
                                    <div class="bg-success text-white rounded-circle p-2 me-3 flex-shrink-0">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    <div>
                                        <h4 class="h6 mb-1">Phone Support</h4>
                                        <p class="text-muted mb-0">+****************</p>
                                        <small class="text-muted">Mon-Fri, 9AM-6PM EST</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="contact-item mb-4">
                                <div class="d-flex align-items-start">
                                    <div class="bg-info text-white rounded-circle p-2 me-3 flex-shrink-0">
                                        <i class="fas fa-comments"></i>
                                    </div>
                                    <div>
                                        <h4 class="h6 mb-1">Live Chat</h4>
                                        <p class="text-muted mb-0">Available in-app</p>
                                        <small class="text-muted">Mon-Fri, 9AM-6PM EST</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="contact-item">
                                <div class="d-flex align-items-start">
                                    <div class="bg-warning text-white rounded-circle p-2 me-3 flex-shrink-0">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    <div>
                                        <h4 class="h6 mb-1">Office</h4>
                                        <p class="text-muted mb-0">
                                            123 Business Ave<br>
                                            Suite 100<br>
                                            New York, NY 10001
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

                <!-- FAQ Section -->
                <div class="row mt-5">
                    <div class="col-lg-12">
                        <div class="feature-card-alt">
                            <h2 class="h3 mb-4">Frequently Asked Questions</h2>
                            
                            <div class="accordion" id="faqAccordion">
                                <div class="accordion-item">
                                    <h3 class="accordion-header" id="faq1">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                            How quickly can I get started with <?php echo $appName; ?>?
                                        </button>
                                    </h3>
                                    <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            You can get started immediately! Simply sign up for a free account, and you'll be tracking time within minutes. No credit card required for the trial.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h3 class="accordion-header" id="faq2">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                            Do you offer customer support?
                                        </button>
                                    </h3>
                                    <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            Yes! We offer email support for all users, with priority support for paid plans. Our team typically responds within 24 hours, often much faster.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h3 class="accordion-header" id="faq3">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                            Can I import data from other time tracking tools?
                                        </button>
                                    </h3>
                                    <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            Yes, we support data import from most popular time tracking tools. Contact our support team for assistance with migration.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<style>
/* Contact Page Styles */
.contact-stat {
    padding: 0.5rem;
}

.contact-form-card {
    background: white;
    border-radius: 1rem;
    padding: 2.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-form-header {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 1.5rem;
    margin-bottom: 2rem;
}

.input-group-text {
    background: #f8fafc;
    border-color: #e5e7eb;
    color: #6b7280;
}

.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.contact-submit-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.contact-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.contact-submit-btn:disabled {
    opacity: 0.7;
    transform: none;
}

.btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
}

.contact-item {
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 0.5rem;
}

.contact-item:hover {
    background: #f8fafc;
    transform: translateX(5px);
}

.feature-card-alt {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.feature-card-alt:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

@media (max-width: 768px) {
    .contact-form-card {
        padding: 1.5rem;
    }

    .contact-stat {
        margin-bottom: 1rem;
    }

    .d-flex.gap-4 {
        flex-direction: column;
        gap: 0 !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('contactForm');
    const submitBtn = document.querySelector('.contact-submit-btn');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');

    // Form validation
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Reset validation
        form.classList.remove('was-validated');

        // Validate form
        let isValid = true;
        const name = document.getElementById('name');
        const email = document.getElementById('email');
        const subject = document.getElementById('subject');
        const message = document.getElementById('message');

        // Name validation
        if (name.value.trim().length < 2) {
            name.setCustomValidity('Name must be at least 2 characters long');
            isValid = false;
        } else {
            name.setCustomValidity('');
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email.value)) {
            email.setCustomValidity('Please enter a valid email address');
            isValid = false;
        } else {
            email.setCustomValidity('');
        }

        // Subject validation
        if (!subject.value) {
            subject.setCustomValidity('Please select a subject');
            isValid = false;
        } else {
            subject.setCustomValidity('');
        }

        // Message validation
        if (message.value.trim().length < 10) {
            message.setCustomValidity('Message must be at least 10 characters long');
            isValid = false;
        } else {
            message.setCustomValidity('');
        }

        form.classList.add('was-validated');

        if (isValid && form.checkValidity()) {
            // Show loading state
            submitBtn.disabled = true;
            btnText.classList.add('d-none');
            btnLoading.classList.remove('d-none');

            // Submit the form
            setTimeout(() => {
                form.submit();
            }, 500);
        }
    });

    // Real-time validation feedback
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (form.classList.contains('was-validated')) {
                this.checkValidity();
            }
        });

        input.addEventListener('input', function() {
            if (form.classList.contains('was-validated')) {
                this.setCustomValidity('');
                this.checkValidity();
            }
        });
    });
});
</script>
